# Table Changes Analysis Report: optimised.py vs prod.py

## Executive Summary

This report analyzes the differences between `optimised.py` and `prod.py` for the Snowflake stored procedure `USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD`. The analysis focuses on identifying tables with the most significant changes and understanding the reasons behind these modifications.

## Tables Analyzed

The stored procedure primarily affects three main tables:
1. **GENESIS_DAY_DURATIONS_HISTORICAL**
2. **GENESIS_PROCEDURE_DATA_HISTORICAL** 
3. **GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS**

---

## 1. GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
**Impact Level: HIGH** 🔴

### Major Changes Identified:

#### A. Quarter Logic Enhancement
- **Change**: Updated current quarter logic to use `running_qtr` instead of `current_qtr` for Q3 2025 data processing
- **Location**: Lines in `adjust_zero_proc_days_current_qtr` and `grab_correct_available_business_days` UDFs
- **Reason**: Proper handling of Q3 2025 data inclusion
- **Impact**: Ensures accurate data processing for the current quarter

#### B. Mathematical Accuracy Improvements
- **Change**: Enhanced percentage calculations with corrected logic for `PROC_DAYS_X_PERCENTAGE` and `PLUS_PROC_DAYS_3_PLUS_PERCENTAGE`
- **Location**: Lines 1400-1600 in optimised.py
- **Reason**: Mathematical accuracy improvements for business metrics
- **Impact**: More precise percentage calculations for procedure day metrics

#### C. Weekly Variance Analysis ("ZERO VARIANCE TARGET")
- **Change**: Implemented precise pandas `resample('W')` logic matching using Snowpark DataFrames
- **Location**: Lines 1400-1600 in optimised.py
- **Reason**: Accurate weekly feature calculations for variance analysis
- **Impact**: Better statistical analysis capabilities

#### D. Data Quality Validation
- **Change**: Added comprehensive variance breakdown analysis with SQL queries
- **Tables Created**: 
  - `VARIANCE_BREAKDOWN_ANALYSIS`
  - `MAX_VARIANCE_CASES`
  - `ZERO_VARIANCE_VALIDATION`
- **Reason**: Compare production vs sandbox data to identify discrepancies
- **Impact**: Enhanced data quality monitoring

#### E. Enhanced Debugging Features
- **Change**: Added debug information insertion into `EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT`
- **Location**: Multiple locations throughout optimised.py
- **Reason**: Better troubleshooting and data validation capabilities
- **Impact**: Improved operational monitoring

---

## 2. GENESIS_PROCEDURE_DATA_HISTORICAL
**Impact Level: MEDIUM** 🟡

### Key Changes:

#### A. Gap Analysis Function Enhancement
- **Change**: Fixed return count in `grab_gap_data` function to match production
- **Location**: Lines 280-330 in optimised.py
- **Before**: `return (None, None)` on exception
- **After**: `return (None, None, None)` to match expected tuple structure
- **Reason**: Consistency with production logic and proper error handling

#### B. NumPy 2.0 Compatibility
- **Change**: Replaced `np.NaN` with `np.nan` in gap analysis
- **Location**: Line 286 in both files (already fixed in prod.py)
- **Reason**: NumPy 2.0 compatibility requirements
- **Impact**: Prevents deprecation warnings and ensures future compatibility

#### C. Enhanced Error Handling
- **Change**: Added warning messages for missing gap data
- **Location**: `grab_gap_data` function in optimised.py
- **Reason**: Better error visibility and debugging
- **Impact**: Improved operational monitoring

---

## 3. GENESIS_DAY_DURATIONS_HISTORICAL
**Impact Level: MEDIUM** 🟡

### Key Changes:

#### A. Data Type Handling
- **Change**: Enhanced string casting for START_TIME and END_TIME columns
- **Location**: `write_day_durations` function
- **Reason**: Consistent data type handling in Snowpark DataFrames
- **Impact**: Prevents data type conversion issues

#### B. Infinity Value Replacement
- **Change**: Consistent handling of `np.inf` values in UTILIZATION_PERCENTAGE_DAY
- **Location**: `write_day_durations` function
- **Reason**: Data quality and mathematical consistency
- **Impact**: Prevents invalid percentage calculations

#### C. Time Aggregation Improvements
- **Change**: Enhanced grouping and aggregation for `AVERAGE_START_TIME` and `AVERAGE_END_TIME`
- **Location**: Lines 1800-2051 in optimised.py
- **Reason**: More accurate time-based analytics
- **Impact**: Better operational insights

---

## 4. Additional Enhancements

### A. Performance Optimizations
- **Pandas to Snowpark Conversion**: Continued migration from pandas operations to Snowpark DataFrames
- **Reason**: Better performance and scalability in Snowflake environment
- **Impact**: Reduced memory usage and improved execution speed

### B. Data Quality Fixes
- **Q1 2025 Data Quality**: Restored original fillna logic and enhanced NULL handling
- **Reason**: Address data quality issues identified in Q1 2025
- **Impact**: More reliable data processing

### C. Statistical Accuracy
- **Change**: Exact production statistics logic implementation in `grab_statistics`
- **Reason**: Mathematical accuracy for statistical calculations
- **Impact**: Consistent statistical metrics across environments

---

## Summary of Changes by Priority

### High Priority Changes:
1. **Quarter Logic Updates** - Critical for Q3 2025 data processing
2. **Mathematical Accuracy** - Essential for business metrics reliability
3. **Variance Analysis** - Important for data quality monitoring

### Medium Priority Changes:
1. **Error Handling Improvements** - Better operational stability
2. **NumPy Compatibility** - Future-proofing the codebase
3. **Data Type Consistency** - Prevents runtime errors

### Low Priority Changes:
1. **Enhanced Debugging** - Operational convenience
2. **Performance Optimizations** - Incremental improvements

---

## Recommendations

1. **Immediate Action**: Deploy the quarter logic fixes to ensure Q3 2025 data is processed correctly
2. **Testing**: Validate the mathematical accuracy improvements with historical data
3. **Monitoring**: Implement the variance analysis queries for ongoing data quality monitoring
4. **Documentation**: Update operational procedures to include new debugging capabilities

---

## Technical Details

### Code Complexity:
- **optimised.py**: 2,051 lines (significantly expanded)
- **prod.py**: 1,366 lines (baseline)
- **Increase**: ~50% more code due to enhanced features and debugging

### Key Functions Modified:
- `grab_gap_data`: Enhanced error handling and return consistency
- `grab_statistics`: Exact production logic implementation
- `adjust_zero_proc_days_current_qtr`: Quarter logic fixes
- `grab_correct_available_business_days`: Quarter logic fixes

### New Features Added:
- Variance breakdown analysis
- Enhanced debugging capabilities
- Weekly variance calculations
- Data quality validation queries

---

*Report generated on: $(date)*
*Analysis based on comparison between optimised.py (2,051 lines) and prod.py (1,366 lines)*