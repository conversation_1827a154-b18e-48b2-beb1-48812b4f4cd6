CREATE OR R<PERSON>LACE PROCEDURE EDWSBX.TRAINING.USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD("BATCHID" FLOAT)
RETURNS VARCHAR(16777216)
LANGUAGE PYTHON
RUNTIME_VERSION = '3.10'
PACKAGES = ('snowflake-snowpark-python','pandas','numpy==1.24.3','scipy')
HANDLER = 'execute'
EXECUTE AS CALLER
AS $$
"""
/* ****************************************************************************
Name:			  [TRAINING].[USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD]
 
** Desc:          

** Execution      EXEC [TRAINING].[USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD] @BatchID = 9999 

** Author:        <PERSON><PERSON><PERSON>
** Date:          2023/07/24

*******************************************************************************
**       CHANGE HISTORY
*******************************************************************************
**  Date			Author				 Description
**  ----------  --------------  ----------------------------------------------------
**  2023/07/24   Piyush Singhal		Initial code
**  2023/08/30   Prasad Surapaneni	Initial code with snake case columns in system_level_historical chunks, procedure_data and day_durations tables #DMND0026104
**  2023/10/25   Prasad Surapaneni  Converted pandas dataframe to snowpark dataframe - DMND0028068
**  2023/12/01   Prasad Surapaneni  Adjusted "grab_correct_businessday_list" function to load the running quarter data in day_durations_historical and system_historical_chunks tables - DMND0028569
**  2025/01/XX   AI Assistant      Fixed Q1 2025 data quality issues: restored original fillna logic, removed aggressive data validation filter, enhanced NULL handling for PROC_DAYS calculations
**  2025/07/25   AI Assistant      Fixed Q3 2025 data inclusion: updated current quarter logic to use running_qtr instead of current_qtr for proper Q3 2025 data processing
******************************************************************************/
"""

# Import Packages
import sys
# import snowflake.snowpark as snowpark
# from snowflake.snowpark import Session
import pandas as pd
import numpy as np
from pandas.tseries.holiday import USFederalHolidayCalendar
# import timeit
from datetime import datetime, date, time, timedelta
import collections
import scipy.stats as sc
# import unicodedata
from snowflake.snowpark import functions as F
from snowflake.snowpark.functions import udf, col, lit, min as min_
from snowflake.snowpark.types import StringType, DateType, IntegerType, FloatType, ArrayType, TimestampType, MapType
from snowflake.snowpark.window import Window
import warnings

warnings.filterwarnings("ignore")


#function gets ExeID
def get_exe_id(session, BatchID, ExeID, StgStatus, ProcName):
  
    query1 = """SELECT
            MAX(ExecutionID) ExecutionID
            FROM
            EDWSBX.Process.ETLProcessHistory
            WHERE
            ProcessName ='{0}'""".format(ProcName)
    df_res = session.sql(query1).to_pandas()
    ExeID = df_res["EXECUTIONID"].values[0]
    return ExeID

# function loads data
def load_data (session,BATCHID):
    ProcName = "TRAINING.USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD"
    BatchID = BATCHID
    ExeID = 0
    PlaceHolder = ""
    StgCnt = 0
    StgStatus = ""
    UpdatedCnt = 0
    InsertedCnt = 0
    TrgIntialCnt = 0
    TrgFinalCnt = 0
    day_durations_count = 0
    procedure_data_count = 0
    system_chunks_count = 0

    Status="running"

    query = "call EDWSBX.Process.USP_INSERTAUDITDETAILS({0}, '{1}', '{2}')".format(BatchID,ProcName,Status)
    session.sql(query).collect()
    spd = session.sql("select CURRENT_SESSION()").collect()[0][0]
    PlaceHolder = "SPID : "+ spd[0:10]+ "."
    StgStatus = PlaceHolder
    ExeID = get_exe_id(session,BatchID, ExeID,StgStatus,ProcName)

    query1 = "call EDWSBX.Process.USP_LOGDETAILS({0}, {1}, '{2}', '{3}')".format(BatchID,ExeID,StgStatus,ProcName)
    session.sql(query1).collect()

    # Script execution starts here
    start_time = datetime.now()

    add_one = {"1": 2, "2": 3, "3": 4, "4": 1}
    subtract_one = {"1": 4, "2": 1, "3": 2, "4": 3}
    temp_table_lst = []


    @udf(name='previous_qtr', input_types=[IntegerType()], return_type=StringType(), is_permanent=False, replace=True, session=session)
    def previous_qtr(calyearqtr):
        """
        Input: CalYearQtr (String represenation of year + qtr (YYYYQ))
        Returns: (String) The previous quarter of the inputted quarter.
        """
        calyearqtr = str(calyearqtr)
        if calyearqtr[-1] in (["2", "3", "4"]):
            return str(calyearqtr[:-1]) + str(subtract_one[calyearqtr[-1]])
        elif calyearqtr[-1] == "1":
            return str(int(calyearqtr[:-1]) - 1) + str(subtract_one[calyearqtr[-1]])

    def previous_qtr_py(calyearqtr):
        """
        Input: CalYearQtr (String represenation of year + qtr (YYYYQ))
        Returns: (String) The previous quarter of the inputted quarter.
        """
        calyearqtr = str(calyearqtr)
        if calyearqtr[-1] in (["2", "3", "4"]):
            return str(calyearqtr[:-1]) + str(subtract_one[calyearqtr[-1]])
        elif calyearqtr[-1] == "1":
            return str(int(calyearqtr[:-1]) - 1) + str(subtract_one[calyearqtr[-1]])


    @udf(name='next_qtr', input_types=[IntegerType()], return_type=StringType(), is_permanent=False, replace=True, session=session)
    def next_qtr(calyearqtr):
        """
        Input: CalYearQtr (String represenation of year + qtr (YYYYQ))
        Returns: (String) The next quarter of the inputted quarter.
        """
        calyearqtr = str(calyearqtr)
        if calyearqtr[-1] in (["1", "2", "3"]):
            return str(calyearqtr[:-1]) + str(add_one[calyearqtr[-1]])
        elif calyearqtr[-1] == "4":
            return str(int(calyearqtr[:-1]) + 1) + str(add_one[calyearqtr[-1]])

    def next_qtr_py(calyearqtr):
        """
        Input: CalYearQtr (String represenation of year + qtr (YYYYQ))
        Returns: (String) The next quarter of the inputted quarter.
        """
        calyearqtr = str(calyearqtr)
        if calyearqtr[-1] in (["1", "2", "3"]):
            return str(calyearqtr[:-1]) + str(add_one[calyearqtr[-1]])
        elif calyearqtr[-1] == "4":
            return str(int(calyearqtr[:-1]) + 1) + str(add_one[calyearqtr[-1]])



    ### NOTE: edited to set one quarter back, so current quarter is set to just-finished quarter
    def previous_quarter(ref_date):
        current_date = ref_date - timedelta(days=1)
        while current_date.month % 3:
            current_date -= timedelta(days=1)
        return current_date


    # QUARTER LOGIC EXPLANATION (as of July 25, 2025):
    # running_qtr = "20253" (Q3 2025) - the actual current quarter for real-time data
    # current_qtr = "20252" (Q2 2025) - the processing quarter (previous quarter for completed data)
    # current_qtr_offset_minus_1 = "20251" (Q1 2025) - used for data filtering (includes last 3 quarters)

    running_month = pd.Period(date.today(), freq='M')
    running_qtr_str = (str((running_month.asfreq('Q-DEC'))))
    running_qtr = running_qtr_str.replace('Q', '')

    today_date = date.today()
    today_date = previous_quarter(today_date)
    print(today_date)
    month = pd.Period(today_date, freq='M')
    str_current_quarter = (str((month.asfreq('Q-DEC'))))
    current_qtr = str_current_quarter.replace('Q', '')
    current_qtr_offset_minus_1 = previous_qtr_py(current_qtr)
    current_qtr_offset_plus_1 = next_qtr_py(current_qtr)

    print (current_qtr, current_qtr_offset_minus_1, current_qtr_offset_plus_1)


    def grab_all_calyearqtr_list(start_qtr, end_qtr):
        """
        Input: Start_Qtr (String), End_Qtr (String)
        Returns: (List (String)) List of quarters in string format.
        """
        temp = start_qtr
        qtr_list = []
        qtr_list.append(start_qtr)
        if start_qtr > end_qtr:
            return None
        while temp != end_qtr:
            temp = next_qtr_py(temp)
            qtr_list.append(temp)
        return qtr_list


    def grab_correct_businessday_list(x): 
        """
        Input: InstallDate (Datetime), RemoveDate (Datetime)
        Given the Install Date and Remove Date of a certain system, returns a list of quarters in string format that exist between the two dates.
        If systems have not been removed, then the ending quarter will be the current quarter.
        Returns: (List (String)) List of quarters in string format.
        """ 
        if x[0] is None and x[1] is None:
            return None
        if x[0] is None:
            return None
        start_qtr = 'undefined'
        end_qtr = 'undefined'
        for i in range(0, df_qtr_start_end.shape[0]):
            if ((x[0] >= df_qtr_start_end.CAL_QTR_FIRST_BUS_DAY.iloc[i]) & (x[0] <= df_qtr_start_end.CAL_QTR_LAST_BUS_DAY.iloc[i])):
                start_qtr = df_qtr_start_end.CAL_YEAR_QTR[i]   
        if x[1] is None:
            end_qtr = running_qtr;#current_qtr
        else:
            for i in range(0, df_qtr_start_end.shape[0]):
                if ((x[1] >= df_qtr_start_end.CAL_QTR_FIRST_BUS_DAY.iloc[i]) & (x[1] <= df_qtr_start_end.CAL_QTR_LAST_BUS_DAY.iloc[i])):
                    end_qtr = df_qtr_start_end.CAL_YEAR_QTR[i]
        if start_qtr == 'undefined':
            start_qtr = x[2]
        if end_qtr == 'undefined':
            end_qtr = x[3]
            if (end_qtr == current_qtr_offset_plus_1):
                end_qtr = running_qtr;#current_qtr
        if (start_qtr == 'undefined') & (end_qtr == 'undefined'):
            return None
        #print
        # print (start_qtr, end_qtr, grab_all_calyearqtr_list(start_qtr, end_qtr))
        # if start_qtr == running_qtr: #current_qtr
            # return grab_all_calyearqtr_list(current_qtr_offset_minus_1, end_qtr)
        return grab_all_calyearqtr_list(start_qtr, end_qtr)


    # @udf(name='return_percentage', input_types=[IntegerType(), IntegerType()], return_type=FloatType(), is_permanent=False, replace=True, session=session)
    def return_percentage(n_proc_days, business_days):
        """
        Input: ProcedureDays (Int), Number of Business Days (Int)
        Returns: (Float) Percentage of business days where atleast 1 procedure was done.
        """
        if n_proc_days < 1:
            return None
        else:
            return (1.0 * n_proc_days)/(business_days)


    @udf(name='grab_total_available_business_day_set', input_types=[StringType(), DateType(), DateType(), ArrayType(), StringType(), StringType()], return_type=ArrayType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def grab_total_available_business_day_set(qtr_col, install_date, remove_date, bus_day_set, cal_year_start_qtr, cal_year_end_qtr):
        """
        Input: Qtr (String), InstallDate (Datetime), RemoveDate (Datetime), BusinessDay_Set (DatetimeIndex (datetime64[ns]))
        Modifies the passed in DateTimeIndex if the Install Date and/or Remove Date happen within the inputted quarter. If not, the original DateTimeIndex is passed back.
        Returns: (DatetimeIndex) datetime64[ns] dates representing business days in a quarter.
        """ 
        bus_day_set = pd.DatetimeIndex(bus_day_set)
        if install_date is not None:
            installdate_qtr = cal_year_start_qtr
        else:
            installdate_qtr = None
        if remove_date is not None:
            removedate_qtr = cal_year_end_qtr
        else:
            removedate_qtr = None
            
        if installdate_qtr == qtr_col and removedate_qtr == qtr_col:
            return pd.date_range(install_date, remove_date, freq='B')
        elif installdate_qtr == qtr_col:
            return pd.date_range(install_date, pd.to_datetime(bus_day_set.max()), freq='B')
        elif removedate_qtr == qtr_col:
            return pd.date_range(pd.to_datetime(bus_day_set.min()), remove_date, freq='B')
        else:
            return bus_day_set


    def grab_gap_data(x, y):
        """
        Input: x= (AccountID (Str), SystemName(Str), Qtr(String)), y= GroupBy object by (Acccount, System, Qtr)
        Returns: Tuple (List, List, GroupBy Object) Returns a tuple that includes 2 lists of turnover times. The first list returned at index 0 consists of all turnovers observed for the given account, system, qtr.
        The second list returned at index 1 in the tuple consists of all turnovers observed on days with 3+ procedures for the given account, system, qtr. The GroupBy object under index 2 is grouped by the number of
        procedures done.
        """
        try:
            procedures_df = y.get_group((x[0], x[1], x[2]))
        except:
            return (None, None, None)  # OPTION 2 FIX: Match production return count
        negative_shift = procedures_df[["SYSTEM_SERIAL_NUMBER", "PROCEDURE_DATE_LOCAL", "START_TIME_WITH_DATE"]].shift(-1)
        negative_shift.columns = ['NEXT_{0}'.format(x) for x in list(negative_shift.columns)]
        procedures_df = pd.concat([procedures_df, negative_shift], axis=1)
        procedures_df["GAP_AFTER"] = 0
        procedures_df.loc[(procedures_df.PROCEDURE_DATE_LOCAL == procedures_df.NEXT_PROCEDURE_DATE_LOCAL) 
                    & (procedures_df.SYSTEM_SERIAL_NUMBER == procedures_df.SYSTEM_SERIAL_NUMBER), 'GAP_AFTER'] = ((procedures_df.NEXT_START_TIME_WITH_DATE - procedures_df.END_TIME_WITH_DATE)/(np.timedelta64(1, 'm')))
        if len(procedures_df[procedures_df["GAP_AFTER"] != 0].index) > 0:
            procedures_df.GAP_AFTER = procedures_df.GAP_AFTER.astype(np.int64)
            #procedures_df.GAP_AFTER = procedures_df.GAP_AFTER.replace(0, np.NaN)
            procedures_df.GAP_AFTER = procedures_df.GAP_AFTER.replace(0, np.nan)
            return (list(procedures_df[procedures_df.GAP_AFTER > 0].GAP_AFTER), list(procedures_df[(procedures_df.PROCEDURE_COUNT > 2) & (procedures_df.GAP_AFTER > 0)].GAP_AFTER),
                    (procedures_df[procedures_df.GAP_AFTER > 0].groupby(("PROCEDURE_COUNT"))["GAP_AFTER"]))
        
        else:
            # print('error on: ', x)
            #return((np.NaN, np.NaN, np.NaN))
            # FIXED: Better error handling for gap analysis
            print(f'Warning: No valid gap data for account {x[0]}, system {x[1]}, quarter {x[2]}')
            return((np.nan, np.nan, np.nan))


    def grab_statistics(x, y):
        """
        OPTION 2 FIX: Exact production statistics logic for mathematical accuracy
        Input: x=(GroupBy Object) by Number of Procedures Done Each Day, y=(Int) Number of Procedures
        Returns: Tuple (Mean (Float), Median (Float), Standard Deviation (Float), Variance (Float)) Returns a tuple that summary statistics given the number of procedures done each day that was passed in.
        """
        try:
            gaps = x.get_group(y)
        except:
            return (None, None, None, None)
        # OPTION 2 FIX: Use exact production logic - no additional data cleaning
        return (np.round((np.mean(gaps)), 3), np.round((np.median(gaps)), 3), np.round((np.std(gaps)), 3), np.round((sc.variation(gaps)), 3))


    @udf(name='avg_time', input_types=[StringType()], return_type=TimestampType(), is_permanent=False, replace=True, session=session)
    def avg_time(datetimes):
        """
        Input: (Pandas Series) Series of Datetimes.
        Returns: (Timestamp) The average time from the series of Datetimes passed.
        """
        datetimes = datetimes.split(",")
        format = '%Y-%m-%d %H:%M:%S.%f'
        datetimes = [datetime.strptime(dt, format) for dt in datetimes if dt!='']
        if len(datetimes)==0:
            return None
        total = sum(dt.hour * 3600 + dt.minute * 60 + dt.second for dt in datetimes)
        avg = total / len(datetimes)
        minutes, seconds = divmod(int(avg), 60)
        hours, minutes = divmod(minutes, 60)
        return datetime.combine(date(1900, 1, 1), time(hours, minutes, seconds))


    @udf(name='get_percentile_rank', input_types=[FloatType(), StringType()], return_type=FloatType(), is_permanent=False, replace=True, session=session, packages=["scipy"])
    def get_percentile_rank(x, feature_values):
        """
        Input: x=(Average Turnover Time (Float), Qtr (String)), y=Groupby Object by Qtr, Country
        Given an inputted quarter and an average turnover time, returns the percentile rank against the entire system population for that quarter
        and inputted country.
        Returns: (Float) Percentile rank for average turnover time.
        """
        feature_values = [float(val) for val in feature_values.split(",") if (val!='' and val is not None)]
        if x == '' or x is None:
            return None

        return sc.percentileofscore(feature_values, x)


    @udf(name='adjust_zero_proc_days_current_qtr', input_types=[StringType(), FloatType(), FloatType(), DateType()], return_type=FloatType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def adjust_zero_proc_days_current_qtr(qtr, proc_days_zero, nonzero_case_days, cal_qtr_first_bus_day):
        """
        Input: x=(Qtr (String), 0_Proc_Days (Float), NonZero_CaseDays (Float))
        If the passed in quarter is equal to the current quarter, adjusts the calculation for the number of zero case days to account for how far we
        are into the quarter.
        Returns: (Float) # of Zero Case Days
        """
        # FIXED: Use running_qtr instead of current_qtr to properly handle Q3 2025 data
        # running_qtr represents the actual current quarter (Q3 2025 as of July 25, 2025)
        # current_qtr represents the processing quarter (Q2 2025 after previous_quarter adjustment)
        if qtr == running_qtr:
            try:
                start_date = cal_qtr_first_bus_day
                return (pd.date_range(start_date, today_date, freq='B')).size - nonzero_case_days
            except:
                return None
        else:
            return proc_days_zero


    @udf(name='grab_correct_available_business_days', input_types=[StringType(), FloatType(),  DateType()], return_type=FloatType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def grab_correct_available_business_days(qtr, available_bus_days, cal_qtr_first_bus_day):
        """
        Input: x= (Qtr (String), Available_BusinessDays (Float))
        If the passed in quarter is equal to the current quarter, adjusts the calculation for the number of business days to account for how far we
        are into the quarter.
        Returns: (Float) # of Available Business Days
        """
        # FIXED: Use running_qtr instead of current_qtr to properly handle Q3 2025 data
        if qtr == running_qtr:
            try:
                start_date = cal_qtr_first_bus_day
                return (pd.date_range(start_date, today_date, freq='B')).size
            except:
                return None
        else:
            return available_bus_days


    @udf(name='get_business_days_qtr', input_types=[DateType(), DateType()], return_type=ArrayType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def get_business_days_qtr(start_date, end_date):
        """
        Input: start date (date) and end date (date)
        Returns: (list) number of business days between start_date and end_date.
        """
        
        # the block below attempts to account for holidays and remove them from total business days
        # for some reason this calculation isn't being reflected in the final result?
        US_holidays = USFederalHolidayCalendar().holidays(start='2020-01-01', end='2030-12-31')

        business_days =  pd.date_range(start_date, end_date, freq= 'B')
        business_days = pd.to_datetime(business_days)
        business_days = [x for x in business_days if x not in US_holidays]
        business_days = pd.to_datetime(business_days)
        return business_days


    @udf(name='get_num_procedures_counter', input_types=[ArrayType()], return_type=MapType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def get_num_procedures_counter(x):
        """
        Input: list of number of procedures for account_id, system_name and quarter
        Returns: (dict) dictionary with "number of procedures" as key and "days with that many number of procs" as value.
        """
        return dict(collections.Counter(list(x)))


    @udf(name='get_num_day_procs', input_types=[MapType(), StringType()], return_type=IntegerType(), is_permanent=False, replace=True, session=session, packages=["pandas"])
    def get_num_day_procs(x, num_day):
        """
        Input: list of number of procedures for account_id, system_name and quarter
        Returns: (String) The next quarter of the inputted quarter.
        """
        return None if x is None else (x.get(num_day) if num_day in x.keys() else 0)

    @udf(name='calculate_cv_scipy', input_types=[ArrayType(FloatType())], return_type=FloatType(), is_permanent=False, replace=True, session=session, packages=["scipy", "numpy"])
    def calculate_cv_scipy(values):
        """
        Calculate coefficient of variation using scipy.stats.variation to match current-prod.py behavior
        Input: Array of float values
        Returns: Coefficient of variation as calculated by scipy.stats.variation
        """
        import numpy as np
        import scipy.stats as sc
        
        if values is None or len(values) == 0:
            return None
        
        # Filter out None/null values
        clean_values = [v for v in values if v is not None]
        if len(clean_values) == 0:
            return None
            
        try:
            return float(sc.variation(clean_values))
        except:
            return None


    day_durations_table = "EDWSBX.TRAINING.GENESIS_DAY_DURATIONS_HISTORICAL"
    procedure_data_table = "EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL"
    system_chunks_table = "EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS"


    def write_day_durations(spdf_day_durations_all):
        sql = f"""DELETE FROM {day_durations_table} WHERE QTR >= {current_qtr_offset_minus_1} """
        delete_data = session.sql(sql).collect()
        
        spdf_day_durations_all = spdf_day_durations_all.with_column("START_TIME", col("START_TIME").cast(StringType()) )
        spdf_day_durations_all = spdf_day_durations_all.with_column("END_TIME", col("END_TIME").cast(StringType()) )

        spdf_day_durations_all = spdf_day_durations_all.filter(col("QTR") >= current_qtr_offset_minus_1)
        # there's something odd with the below metric. Should not have values >1?
        spdf_day_durations_all = spdf_day_durations_all.replace({np.inf : 1}, subset=["UTILIZATION_PERCENTAGE_DAY"])

        # start = timeit.default_timer()
        l =session.table('EDWSBX.INFORMATION_SCHEMA.columns').filter(f"TABLE_NAME = '{day_durations_table.split('.')[-1]}' and TABLE_SCHEMA = 'TRAINING' ").orderBy("ORDINAL_POSITION")
            
        col_list = []
        for row in l.select("COLUMN_NAME").collect():
            col_list.append(row[0])

        col_list = set(spdf_day_durations_all.columns).intersection(set(col_list))

        # spdf_day_durations_all = spdf_day_durations_all.with_column("BUSINESS_DAY_DATE", F.iff(col("BUSINESS_DAY_DATE")== "NaT", None, col("BUSINESS_DAY_DATE")) )
        # spdf_day_durations_all = spdf_day_durations_all.with_column("BUSINESS_DAY_DATE", col("BUSINESS_DAY_DATE").cast(DateType()) )
        # spdf_day_durations_all = spdf_day_durations_all.with_column("START_TIME", F.iff(col("START_TIME")== "NaT", None, col("START_TIME")) )
        # spdf_day_durations_all = spdf_day_durations_all.with_column("END_TIME", F.iff(col("END_TIME")== "NaT", None, col("END_TIME")) )
        df_Write = spdf_day_durations_all.select(col_list)
        df_Write.write.mode("append").save_as_table(day_durations_table, column_order="name") 

        # stop = timeit.default_timer()
        # print((stop-start)/60.0)


    def write_procedure_data(spdf_procedure_data):
        sql = f"""DELETE FROM {procedure_data_table} WHERE CAL_YEAR_QTR >= {current_qtr_offset_minus_1} """
        delete_data = session.sql(sql).collect()
        
        spdf_procedure_data = spdf_procedure_data.with_column("START_TIME_WITH_DATE", col("START_TIME_WITH_DATE").cast(StringType()) )
        spdf_procedure_data = spdf_procedure_data.with_column("END_TIME_WITH_DATE", col("END_TIME_WITH_DATE").cast(StringType()) )

        spdf_procedure_data = spdf_procedure_data.filter(col("CAL_YEAR_QTR") >= current_qtr_offset_minus_1)

        # start = timeit.default_timer()

        l = session.table('EDWSBX.INFORMATION_SCHEMA.columns').filter(f"TABLE_NAME = '{procedure_data_table.split('.')[-1]}' and TABLE_SCHEMA = 'TRAINING'").orderBy("ORDINAL_POSITION")
        col_list = []
        for row in l.select("COLUMN_NAME").collect():
            col_list.append(row[0])

        col_list = set(spdf_procedure_data.columns).intersection(set(col_list))

        # spdf_procedure_data = spdf_procedure_data.with_column("BUSINESS_DAY_DATE", F.iff(col("BUSINESS_DAY_DATE")=="NaT", None, col("BUSINESS_DAY_DATE")) )
        # spdf_procedure_data = spdf_procedure_data.with_column("BUSINESS_DAY_DATE", col("BUSINESS_DAY_DATE").cast(DateType()) )
        # spdf_procedure_data = spdf_procedure_data.with_column("PROCEDURE_DATE_LOCAL", col("PROCEDURE_DATE_LOCAL").cast(StringType()) ) 
        df_Write = spdf_procedure_data.select(col_list)
        df_Write.write.mode("append").save_as_table(procedure_data_table, column_order="name") 

        # stop = timeit.default_timer()
        # print((stop-start)/60.0)

    def write_system_chunks(spdf_all_systems):
        sql = f"""DELETE FROM {system_chunks_table} WHERE QTR >=  {current_qtr_offset_minus_1} """
        delete_data = session.sql(sql).collect()
        
        spdf_all_systems.rename({
            'MEDIAN_TURNOVER_TIME_MINUTES': 'MEDIAN_TURNOVER_TIME_MINS',
        })

        # quick error handling for inf values in 'Percentage of OR Days in Qtr.' (setting to 0)
        spdf_all_systems = spdf_all_systems.replace({np.inf : 0}, subset=['PERCENTAGE_OR_DAYS_IN_QTR'])

        # start = timeit.default_timer()
        l =session.table('EDWSBX.INFORMATION_SCHEMA.columns').filter(f"TABLE_NAME = '{system_chunks_table.split('.')[-1]}' and TABLE_SCHEMA = 'TRAINING'").orderBy("ORDINAL_POSITION")

        col_list = []
        for row in l.select("COLUMN_NAME").collect():
            col_list.append(row[0])

        col_list = set(spdf_all_systems.columns).intersection(set(col_list))

        spdf_all_systems = spdf_all_systems.with_column("AVERAGE_START_TIME", col("AVERAGE_START_TIME").cast(StringType()) )
        spdf_all_systems = spdf_all_systems.with_column("AVERAGE_START_TIME", F.iff(col("AVERAGE_START_TIME")=="NaT", None, col("AVERAGE_START_TIME")) )
        spdf_all_systems = spdf_all_systems.with_column("AVERAGE_END_TIME", col("AVERAGE_END_TIME").cast(StringType()) )
        spdf_all_systems = spdf_all_systems.with_column("AVERAGE_END_TIME", F.iff(col("AVERAGE_END_TIME")=="NaT", None, col("AVERAGE_END_TIME")) )
        
        df_Write = spdf_all_systems.select(col_list)
        df_Write.write.mode("append").save_as_table(system_chunks_table, column_order="name") 

        # stop = timeit.default_timer()
        # print ((stop-start)/60.0)



    """
    spdf_qtr_start_end is a dataframe which details the first and last business day for every quarter. Quarters are 
    represented in a string format (YYYYQ) by column, CalYearQtr
    """
    sql = """
        SELECT distinct calyearqtr as "CAL_YEAR_QTR", calqtrfirstbusday as "CAL_QTR_FIRST_BUS_DAY", 
        calqtrlastbusday as "CAL_QTR_LAST_BUS_DAY"
        FROM EDW.MASTER.VW_SFDC_ISICALENDAR
        WHERE (country = 'US' and calqtrfirstbusday >= '1998-10-01')
        ORDER BY calyearqtr ASC, calqtrfirstbusday ASC, calqtrlastbusday ASC
        """
    spdf_qtr_start_end = session.sql(sql)#.to_pandas()

    spdf_qtr_start_end = spdf_qtr_start_end.with_column("CAL_YEAR_QTR", col("CAL_YEAR_QTR").cast(StringType()) )
    spdf_qtr_start_end = spdf_qtr_start_end.with_column("PREVIOUS_CAL_YEAR_QTR", previous_qtr(col("CAL_YEAR_QTR")) )
    spdf_qtr_start_end = spdf_qtr_start_end.with_column("NEXT_CAL_YEAR_QTR", next_qtr(col("CAL_YEAR_QTR")) )
    dict_business_days = {}


    spdf_qtr_start_end = spdf_qtr_start_end.with_column("BUSINESS_DAY_SET", get_business_days_qtr(col("CAL_QTR_FIRST_BUS_DAY"), col("CAL_QTR_LAST_BUS_DAY")) )
    df_qtr_start_end = spdf_qtr_start_end.to_pandas()


    # US_holidays = USFederalHolidayCalendar().holidays(start='2020-01-01', end='2030-12-31')
    # for i in range(0, df_qtr_start_end.shape[0]):
    #     cal_year_qtr = df_qtr_start_end["CalYearQtr"].iloc[i]
    #     start_date = df_qtr_start_end["CalQtrFirstBusDay"].iloc[i]
    #     end_date = df_qtr_start_end["CalQtrLastBusDay"].iloc[i]
    #     business_days =  pd.date_range(start_date, end_date, freq= 'B')
    #     business_days = pd.to_datetime(business_days)
    #     business_days = [x for x in business_days if x not in US_holidays]
    #     business_days = pd.to_datetime(business_days)
    #     dict_business_days[cal_year_qtr] = business_days


    sql = """
        SELECT distinct country as "COUNTRY", calday as "CAL_DAY", calyearqtr as "CAL_YEAR_QTR"
        FROM EDW.MASTER.VW_SFDC_ISICALENDAR
        WHERE (country = 'US' and calday <= '2034-01-01' and calday >= '1990-01-01')
        ORDER BY country ASC, calday ASC, calyearqtr ASC
        """
    spdf_specific_date_calyearqtr = session.sql(sql)#.to_pandas()
    # spdf_specific_date_calyearqtr.show()
    # spdf_specific_date_calyearqtr.index = spdf_specific_date_calyearqtr.CAL_DAY

    spdf_specific_date_calyearqtr = spdf_specific_date_calyearqtr.with_column("NEXT_CAL_YEAR_QTR", next_qtr(col("CAL_YEAR_QTR")) )
    # spdf_specific_date_calyearqtr["NEXT_CAL_YEAR_QTR"] = spdf_specific_date_calyearqtr.CAL_YEAR_QTR.apply(next_qtr_py)


    '''
    spdf_total_number_systems is a dataframe that includes details on every system that's located in the US regions.

    '''
    sql = """
        select DISTINCT hospitalname as "ACCOUNT_NAME", accountid as "ACCOUNT_ID",hospital as "HOSPITAL_GUID", systemserialnum as "SYSTEM_SERIAL_NUMBER", 
        model as "MODEL", installdate as "INSTALL_DATE", systeminactiveflag as "INACTIVE_FLAG", type as "TYPE", 
        removedate as "REMOVE_DATE", country as "COUNTRY"
        from EDW.master.vw_installbase
        where country = 'United States'
        ORDER BY hospitalname, systemserialnum ASC, model ASC, installdate ASC,
        systeminactiveflag ASC, type ASC, removedate ASC
        """
    spdf_total_number_systems = session.sql(sql)#.to_pandas()
                                            
    # print ('Total Number Systems: ' + str(spdf_total_number_systems.count()))

    spdf_total_number_systems = spdf_total_number_systems.filter(" TYPE = 'System' and MODEL != 'da Vinci' and ACCOUNT_ID != '16629'" )
    spdf_total_number_systems = spdf_total_number_systems.filter("SYSTEM_SERIAL_NUMBER != 'SH0521' ")



    # add start quarter and end quarter columns based on install date and remove date
    spdf_total_number_systems = spdf_total_number_systems.join(spdf_specific_date_calyearqtr.select("CAL_DAY", col("CAL_YEAR_QTR").as_("CAL_YEAR_START_QTR")), spdf_total_number_systems.INSTALL_DATE == spdf_specific_date_calyearqtr.CAL_DAY, how="left")
    spdf_total_number_systems = spdf_total_number_systems.drop("CAL_DAY")
    spdf_total_number_systems = spdf_total_number_systems.join(spdf_specific_date_calyearqtr.select(col("CAL_DAY"), col("CAL_YEAR_QTR").as_("CAL_YEAR_END_QTR")), spdf_total_number_systems.REMOVE_DATE == spdf_specific_date_calyearqtr.CAL_DAY, how="left")
    spdf_total_number_systems = spdf_total_number_systems.drop("CAL_DAY")
    # conver to pandas dataframe becauseto apply some funtions
    total_number_systems = spdf_total_number_systems.to_pandas()

    total_number_systems["QTR_LIST"] = (total_number_systems[["INSTALL_DATE", "REMOVE_DATE", "CAL_YEAR_START_QTR", 
    "CAL_YEAR_END_QTR"]]).apply(grab_correct_businessday_list, axis=1)



    spdf_total_number_systems = session.create_dataframe(total_number_systems)

    spdf_temp_total_number_systems = spdf_total_number_systems.select(["QTR_LIST", "SYSTEM_SERIAL_NUMBER"])
    spdf_temp_total_number_systems = spdf_temp_total_number_systems.with_column("QTR_LIST", F.to_array("QTR_LIST"))


    spdf_temp_total_number_systems = spdf_temp_total_number_systems.with_column("QTR", F.explode('QTR_LIST'))
    spdf_all_systems = spdf_temp_total_number_systems.join(spdf_total_number_systems, on='SYSTEM_SERIAL_NUMBER', rsuffix="_DUP")
    spdf_all_systems = spdf_all_systems.with_column("QTR", col("QTR").cast(StringType()))
    spdf_all_systems = spdf_all_systems.filter(col("QTR").is_not_null())

    # at the (System Serial Number, Qtr) level, getting the right businessday set to examine
    spdf_all_systems = spdf_all_systems.join(spdf_qtr_start_end, spdf_all_systems.QTR==spdf_qtr_start_end.CAL_YEAR_QTR) # (df_all_systems["Qtr"]).apply(grab_correct_businessday_set)
    # df_all_systems.count()


    '''
    spdf_procedure_data is a dataframe that includes details on procedures done 2018 onwards in the OUS regions.

    '''
    sql = f"""select distinct ps.accountid as "ACCOUNT_ID",
                ps.accountname as "ACCOUNT_NAME",ps.systemname as "SYSTEM_SERIAL_NUMBER",
                ps.casenumber as "PROCEDURE_NUMBER",
                ps.LocalProcedureDate as "PROCEDURE_DATE_LOCAL",
                procs.starttimelocal as "START_TIME_LOCAL",
                (ps.durationinminutes*60) as "PROCEDURE_DURATION",
                ps.surgeonname as "SURGEON_NAME",
                ps.surgeonid as "SURGEON_ID", ps.surgeonspecialtyname as "SURGEON_SPECIALITY",
                ib.systemlocationtype as "SYSTEM_LOCATION_TYPE", ps.category as "BUSINESS_CATEGORY",
                ps.procedurename as "PROCEDURE_NAME", ps.ProcedureSubject as "SUBJECT",
                cal.CALYEARQTR as "CAL_YEAR_QTR", procs.systemmodel as "MODEL",
                ib.installdate as "INSTALL_DATE", ib.country as "COUNTRY"
                from EDW.PROCEDURES.VW_PROCEDURESUMMARY ps
                left join EDW.procedures.vw_procedures procs on procs.casenumber=ps.casenumber
                left join EDW.master.vw_installbase  ib on procs.installedbaseguid = ib.installbaseguid
                left join EDW.master.vw_sfdc_isicalendar cal on procs.proceduredatelocal = cal.calday
                where ps.CaseStatus = 'Completed' and ps.primarylocation = 'US'
                and ps.LocalProcedureDate >'2018-01-01'
                and  cal.CALYEARQTR >= {current_qtr_offset_minus_1}
                ORDER BY ps.accountid ASC, ps.systemname ASC, ps.casenumber ASC, ps.LocalProcedureDate ASC, procs.starttimelocal ASC
            """
    spdf_procedure_data = session.sql(sql)#.to_pandas()

    # stop = timeit.default_timer()

    # print ('Total Time: ' + str((stop - start)/60.0) + ' minutes')

    # Some data manipulations for easier analysis later on
    spdf_procedure_data = spdf_procedure_data.fillna('00:00 AM', subset=["START_TIME_LOCAL"])
    spdf_procedure_data = spdf_procedure_data.with_column("STRING_START_TIME_LOCAL", col("START_TIME_LOCAL").cast(StringType()))
    spdf_procedure_data = spdf_procedure_data.filter(F.length(col("STRING_START_TIME_LOCAL")) == 8)
    # spdf_procedure_data["DATE_START_TIME_LOCAL"] = pd.to_datetime(spdf_procedure_data.STRING_START_TIME_LOCAL.apply(lambda x: unicodedata.normalize('NFKC', x))
    spdf_procedure_data = spdf_procedure_data.with_column("24_HOUR_START_TIME", F.to_time(col("STRING_START_TIME_LOCAL")) )
    spdf_procedure_data = spdf_procedure_data.with_column("START_TIME_WITH_DATE", F.timestamp_from_parts(col("PROCEDURE_DATE_LOCAL"), col("24_HOUR_START_TIME")) )
    columns = ['STRING_START_TIME_LOCAL', '24_HOUR_START_TIME']
    spdf_procedure_data = spdf_procedure_data.drop(columns)
    spdf_procedure_data = spdf_procedure_data.with_column("PROCEDURE_DATE_LOCAL", F.to_timestamp("PROCEDURE_DATE_LOCAL") )
    spdf_procedure_data = spdf_procedure_data.with_column("HOUR_OF_PROCEDURE_START", F.hour("START_TIME_WITH_DATE") )

    # print ('Total Number of Accounts: ' + str(total_number_systems.AccountID.nunique()))
    # print ('Total Number of Systems: ' + str(total_number_systems.SystemSerialNumber.nunique()))

    # total_number_systems = total_number_systems.with_column("PROCS_PAST_12", IntegerType())
    # total_number_systems = total_number_systems.with_column("TOTAL_OR_DAYS", IntegerType())


    # print (str(spdf_procedure_data[spdf_procedure_data.ProcedureDuration.isnull()].shape[0]) + " procedures didn't have durations. We've flagged them so that they don't appear in our analysis.")
    # print ("We're looking at a total of " + str(spdf_procedure_data[~spdf_procedure_data.ProcedureDuration.isnull()].shape[0]) + " valid procedures.")

    # When procedures don't have durations, it means that they are offsite. We don't want to include procedures done on offsite systems. 

    spdf_procedure_data = spdf_procedure_data.filter(col("PROCEDURE_DURATION").is_not_null())
    # Piyush Changes
    # spdf_procedure_data['ProcedureDuration']= spdf_procedure_data['ProcedureDuration'].astype('timedelta64[s]').dt.seconds

    spdf_procedure_data = spdf_procedure_data.with_column("PROCEDURE_DURATION", col("PROCEDURE_DURATION").cast(FloatType()) )
    spdf_procedure_data = spdf_procedure_data.sort(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "START_TIME_WITH_DATE"])

    # Data manipulations and group by objects for faster data analysis later on
    # Piyush optimized code for add seconds
    # spdf_procedure_data["EndTimeWithDate"] = spdf_procedure_data[["StartTimeWithDate", "ProcedureDuration"]].apply(add_seconds, axis=1)
    spdf_procedure_data = spdf_procedure_data.with_column("END_TIME_WITH_DATE", F.dateadd("second", col("PROCEDURE_DURATION"), col("START_TIME_WITH_DATE")) )
    spdf_procedure_data = spdf_procedure_data.with_column("HOUR_OF_PROCEDURE_END", F.hour("END_TIME_WITH_DATE") )


    # We only want to include onsite systems (systems with some procedure duration data)
    # systems_with_durations = list(spdf_procedure_data.select("SYSTEM_SERIAL_NUMBER") )
    spdf_total_number_systems = spdf_total_number_systems.filter(col("SYSTEM_SERIAL_NUMBER").in_(spdf_procedure_data.select("SYSTEM_SERIAL_NUMBER")))


    # We only want to include quarters from 20181 onwards
    # spdf_all_systems = spdf_all_systems[spdf_all_systems.Qtr.isin(grab_all_calyearqtr_list('20181', current_qtr))]
    spdf_all_systems = spdf_all_systems.filter(col("QTR") >= current_qtr_offset_minus_1)


    spdf_all_systems = spdf_all_systems.with_column("AVAILABLE_BUSINESS_DAY_SET", grab_total_available_business_day_set(col("QTR"), col("INSTALL_DATE"), col("REMOVE_DATE"), col("BUSINESS_DAY_SET"), col("CAL_YEAR_START_QTR"), col("CAL_YEAR_END_QTR")) )
    spdf_all_systems = spdf_all_systems.with_column("AVAILABLE_BUSINESS_DAYS", F.array_size("AVAILABLE_BUSINESS_DAY_SET") )
    spdf_all_systems = spdf_all_systems.with_column("FULLY_AVAILABLE_QUARTER", F.array_size("AVAILABLE_BUSINESS_DAY_SET") == F.array_size("BUSINESS_DAY_SET") )


    # ## Optimization start

    # data_group_by = list(df_all_systems.group_by(by=["AccountID_Column","System Serial Number", "Qtr_Column"]).groups.keys())
    # day_durations is a dataframe which tracks system usage data at the day level
    # spdf_day_durations_all = pd.DataFrame(data_group_by, columns =['AccountID', 'SystemName', 'Qtr'])

    # day_durations is a dataframe which tracks system usage data at the day level
    spdf_day_durations_all = spdf_all_systems.drop_duplicates(["ACCOUNT_ID","SYSTEM_SERIAL_NUMBER", "QTR"]).select(["ACCOUNT_ID","ACCOUNT_NAME","SYSTEM_SERIAL_NUMBER", "QTR", "BUSINESS_DAY_SET"])
    # may need to use the below statement if strictly need to take the first occurence
    # df.select(rank().over(Window.partition_by(col("X")).order_by(col("Y"))).alias("result")).show()
    # df_all_systems_temp = df_all_systems.drop_duplicates(subset=["AccountID_Column","System Serial Number", "Qtr_Column"], keep='first')
    # df_all_systems_temp = df_all_systems_temp[["AccountID_Column","System Serial Number", "Qtr_Column", "BusinessDay_Set", "AccountName" ]]

    spdf_day_durations_all = spdf_day_durations_all.with_column("BUSINESS_DAY_DATE", F.explode("BUSINESS_DAY_SET"))
    spdf_day_durations_all = spdf_day_durations_all.with_column("BUSINESS_DAY_DATE", col("BUSINESS_DAY_DATE").cast(DateType()))

    spdf_system_df = spdf_procedure_data.drop_duplicates(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"]).select(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"]) #, keep='first')
    spdf_day_durations_all = spdf_day_durations_all.join(spdf_system_df, (spdf_day_durations_all.ACCOUNT_ID == spdf_system_df.ACCOUNT_ID) & (spdf_day_durations_all.SYSTEM_SERIAL_NUMBER == spdf_system_df.SYSTEM_SERIAL_NUMBER) & (spdf_day_durations_all.QTR == spdf_system_df.CAL_YEAR_QTR), rsuffix="_DUP" )
    spdf_day_durations_all = spdf_day_durations_all.with_column("QTR", col("CAL_YEAR_QTR") )
    spdf_day_durations_all = spdf_day_durations_all.drop(["CAL_YEAR_QTR"])
    # print(spdf_day_durations_all.shape)
    # print(spdf_day_durations_all.groupby(by=["AccountID","SystemName", "Qtr"]).ngroups)


    # Calculate Afternoon Hours
    spdf_procedure_data_temp = spdf_procedure_data#.copy()
    spdf_procedure_data_temp = spdf_procedure_data_temp.with_column("AFTERNOON_HOURS", lit(0))
    spdf_procedure_data_temp = spdf_procedure_data_temp.with_column("AFTERNOON_HOURS", F.iff((col("HOUR_OF_PROCEDURE_END") == 11) & (col("HOUR_OF_PROCEDURE_START") < 11), col("AFTERNOON_HOURS") + (F.minute(col("END_TIME_WITH_DATE")) + 25), col("AFTERNOON_HOURS")) )

    spdf_procedure_data_temp = spdf_procedure_data_temp.with_column("AFTERNOON_HOURS", F.iff((col("HOUR_OF_PROCEDURE_END") == 11) & (col("HOUR_OF_PROCEDURE_START") == 11), col("AFTERNOON_HOURS") + (col("PROCEDURE_DURATION")/60.0) + 50, col("AFTERNOON_HOURS")) )

    spdf_procedure_data_temp = spdf_procedure_data_temp.with_column("AFTERNOON_HOURS", F.iff((col("HOUR_OF_PROCEDURE_END") >= 11) & (col("HOUR_OF_PROCEDURE_START") >= 11), col("AFTERNOON_HOURS") + (col("PROCEDURE_DURATION")/60.0) + 50, col("AFTERNOON_HOURS")) )

    spdf_procedure_data_temp = spdf_procedure_data_temp.with_column("AFTERNOON_HOURS", F.iff((col("HOUR_OF_PROCEDURE_END") > 11) & (col("HOUR_OF_PROCEDURE_START") < 11), col("AFTERNOON_HOURS") + ((col("HOUR_OF_PROCEDURE_END") - 11) * 60.0) + F.minute(col("END_TIME_WITH_DATE")) + 25, col("AFTERNOON_HOURS")) )

    spdf_procedure_data_groupby_account_system_day = spdf_procedure_data_temp.group_by(
        ["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "PROCEDURE_DATE_LOCAL"])

    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_account_system_day.agg(
        F.count('PROCEDURE_DURATION').alias("PROCEDURE_DURATION_SIZE"),
        F.sum('PROCEDURE_DURATION').alias("PROCEDURE_DURATION_SUM"),
        F.min('START_TIME_WITH_DATE').alias("START_TIME_WITH_DATE_MIN"),
        F.max('END_TIME_WITH_DATE').alias("END_TIME_WITH_DATE_MAX"),
        F.count_distinct('SURGEON_ID').alias("SURGEON_ID_NUNIQUE"),
        F.sum('AFTERNOON_HOURS').alias("AFTERNOON_HOURS_SUM"),
        F.max('HOUR_OF_PROCEDURE_END').alias("HOUR_OF_PROCEDURE_END_MAX"),
        F.max('HOUR_OF_PROCEDURE_START').alias("HOUR_OF_PROCEDURE_START_MAX"),
        )

    # Calculate Time_System_Used column via grab_time_used function
    # spdf_procedure_data_groupby_aggregation.columns = ['_'.join(col).strip("_") for col in spdf_procedure_data_groupby_aggregation.columns]
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("GRAB_TIME_USED", col("PROCEDURE_DURATION_SUM")/3600 + col("PROCEDURE_DURATION_SIZE") * 0.833)
    


    # Calculate Total_Operating_Hours column via grab_total_operating_hours function
    # spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("GRAB_TOTAL_OPERATING_HOURS", (col("END_TIME_WITH_DATE_MAX") + pd.Timedelta(minutes=25)) - (col("START_TIME_WITH_DATE_MIN") + pd.Timedelta(minutes=-25))
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("GRAB_TOTAL_OPERATING_HOURS", F.datediff('second', F.dateadd("minute", lit(-25), col("START_TIME_WITH_DATE_MIN")), F.dateadd("minute", lit(25), col("END_TIME_WITH_DATE_MAX"))) )
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("GRAB_TOTAL_OPERATING_HOURS", F.round(col("GRAB_TOTAL_OPERATING_HOURS")/3600.0, 2) )
    # spdf_procedure_data_groupby_aggregation["grab_total_operating_hours"] = np.round(spdf_procedure_data_groupby_aggregation["grab_total_operating_hours"]/3600.0, 2)

    # Calculate Num_Procedures column via grab_num_procedures function
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("PROCEDURE_COUNT", col("PROCEDURE_DURATION_SIZE") )

    # Calculate Num_Procedures_Day column via grab_num_procedures function
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("PROCEDURE_DAYS", col("PROCEDURE_DURATION_SIZE") )

    # Calculate Surgeon Days column via grab_surgeon_day function
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("SURGEON_DAYS", col("SURGEON_ID_NUNIQUE") )
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("SURGEON_DAYS", F.iff(col("SURGEON_DAYS") == 1, 1, 0) )

    # Calculate StartTime column via grab_start_time function
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("START_TIME", F.dateadd("minute", lit(-25), col("START_TIME_WITH_DATE_MIN")) )

    # Calculate EndTime column via grab_start_time function
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("END_TIME", F.dateadd("minute", lit(25), col("END_TIME_WITH_DATE_MAX")) )

    # Calculate Afternoon_Hours final
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("AFTERNOON_HOURS_SUM", col("AFTERNOON_HOURS_SUM")/60.0)

    # Calculate Start Past 11
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("START_PAST_11_AM", F.iff((col("HOUR_OF_PROCEDURE_END_MAX") >= 11) & (col("HOUR_OF_PROCEDURE_START_MAX") >= 11), 1, 0))

    # Calculate Start Past 2
    spdf_procedure_data_groupby_aggregation = spdf_procedure_data_groupby_aggregation.with_column("START_PAST_2_PM", F.iff((col("HOUR_OF_PROCEDURE_END_MAX") >= 14) & (col("HOUR_OF_PROCEDURE_START_MAX") >= 14), 1, 0) )



    spdf_day_durations_all = spdf_day_durations_all.join(spdf_procedure_data_groupby_aggregation,
        (spdf_day_durations_all.ACCOUNT_ID == spdf_procedure_data_groupby_aggregation.ACCOUNT_ID) & (spdf_day_durations_all.SYSTEM_SERIAL_NUMBER == spdf_procedure_data_groupby_aggregation.SYSTEM_SERIAL_NUMBER) & (spdf_day_durations_all.BUSINESS_DAY_DATE == spdf_procedure_data_groupby_aggregation.PROCEDURE_DATE_LOCAL), how="left", rsuffix="_DUP2")

    spdf_day_durations_all = spdf_day_durations_all.rename({
        'GRAB_TIME_USED': 'TIME_SYSTEM_USED',
        'GRAB_TOTAL_OPERATING_HOURS': 'TOTAL_OPERATING_HOURS',
        'AFTERNOON_HOURS_SUM': 'AFTERNOON_HOURS'})

    # Calculate Utilization_Percentage_Day column
    spdf_day_durations_all = spdf_day_durations_all.with_column("UTILIZATION_PERCENTAGE_DAY", (col("TIME_SYSTEM_USED") * 1.0)/(col("TOTAL_OPERATING_HOURS")) )

    spdf_day_durations_all = spdf_day_durations_all.drop([
        'PROCEDURE_DATE_LOCAL', 'PROCEDURE_DURATION_SIZE', 'PROCEDURE_DURATION_SUM',
        'START_TIME_WITH_DATE_MIN', 'END_TIME_WITH_DATE_MAX', 'SURGEON_ID_NUNIQUE', 'HOUR_OF_PROCEDURE_END_MAX', 
        'HOUR_OF_PROCEDURE_START_MAX'])



    # Final data modifications - FIXED: Restore original fillna behavior for Q1 2025 data consistency
    # Use original fillna logic to ensure consistent data handling across all quarters
    spdf_day_durations_all = spdf_day_durations_all.fillna(0, ["TIME_SYSTEM_USED", "TOTAL_OPERATING_HOURS", "PROCEDURE_COUNT", "PROCEDURE_DAYS", "SURGEON_DAYS", "AFTERNOON_HOURS", "START_PAST_11_AM", "START_PAST_2_PM"])
    # spdf_day_durations_all.index = spdf_day_durations_all.BUSINESS_DAY_DATE
    spdf_day_durations_all = spdf_day_durations_all.with_column("TIME_SYSTEM_USED", col("TIME_SYSTEM_USED").cast(FloatType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("TOTAL_OPERATING_HOURS", col("TOTAL_OPERATING_HOURS").cast(FloatType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("UTILIZATION_PERCENTAGE_DAY", col("UTILIZATION_PERCENTAGE_DAY").cast(FloatType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column('DAY_OF_WEEK', F.dayofweek(col("BUSINESS_DAY_DATE")))

    spdf_day_durations_all = spdf_day_durations_all.with_column("PROCEDURE_COUNT", col("PROCEDURE_COUNT").cast(IntegerType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("PROCEDURE_DAYS", col("PROCEDURE_DAYS").cast(IntegerType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("SURGEON_DAYS", col("SURGEON_DAYS").cast(IntegerType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("DAY_OF_WEEK", col("DAY_OF_WEEK").cast(StringType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("START_PAST_11_AM", col("START_PAST_11_AM").cast(IntegerType()) )
    spdf_day_durations_all = spdf_day_durations_all.with_column("START_PAST_2_PM", col("START_PAST_2_PM").cast(IntegerType()) )
    spdf_day_durations_all = spdf_day_durations_all.rename("SYSTEM_SERIAL_NUMBER", "SYSTEM_NAME")

    # change day_of_week value from number to actual week day name
    spdf_day_durations_all = spdf_day_durations_all.with_column("DAY_OF_WEEK", 
                                F.iff(col("DAY_OF_WEEK")==0, "Sunday", 
                                    F.iff(col("DAY_OF_WEEK")==1, "Monday",
                                        F.iff(col("DAY_OF_WEEK")==2, "Tuesday",
                                            F.iff(col("DAY_OF_WEEK")==3, "Wednesday",
                                                F.iff(col("DAY_OF_WEEK")==4, "Thursday",
                                                    F.iff(col("DAY_OF_WEEK")==5, "Friday",
                                                        F.iff(col("DAY_OF_WEEK")==6, "Saturday", None
                                ))))))))

    # Reorder Columns
    spdf_day_durations_all = spdf_day_durations_all.select(['ACCOUNT_NAME', 'ACCOUNT_ID', 'SYSTEM_NAME', 'BUSINESS_DAY_DATE',
            'TIME_SYSTEM_USED', 'PROCEDURE_COUNT', 'PROCEDURE_DAYS', 'AFTERNOON_HOURS', 'QTR',
            'TOTAL_OPERATING_HOURS', 'UTILIZATION_PERCENTAGE_DAY', 'START_PAST_11_AM',
            'START_PAST_2_PM', 'SURGEON_DAYS', 'START_TIME', 'END_TIME', 'DAY_OF_WEEK'])


    # spdf_day_durations_all.schema
    # spdf_day_durations_all.count()


    # Add data quality validation without filtering out records
    data_quality_check = spdf_day_durations_all.agg(
        F.count("*").alias("total_records"),
        F.count(col("PROCEDURE_COUNT")).alias("non_null_procedure_count"),
        F.count(col("PROCEDURE_DAYS")).alias("non_null_procedure_days"),
        F.count(col("SURGEON_DAYS")).alias("non_null_surgeon_days")
    ).collect()[0]

    print(f"Data Quality Check - Total: {data_quality_check['TOTAL_RECORDS']}, "
          f"Non-null PROCEDURE_COUNT: {data_quality_check['NON_NULL_PROCEDURE_COUNT']}, "
          f"Non-null PROCEDURE_DAYS: {data_quality_check['NON_NULL_PROCEDURE_DAYS']}, "
          f"Non-null SURGEON_DAYS: {data_quality_check['NON_NULL_SURGEON_DAYS']}")

    # write day durations data to the database
    spdf_day_durations_all = spdf_day_durations_all.filter(" BUSINESS_DAY_DATE <= CURRENT_DATE() ")
    write_day_durations(spdf_day_durations_all)


    spdf_day_durations_all = session.table(day_durations_table).filter(col("QTR") >= current_qtr_offset_minus_1)


    spdf_num_procedures = spdf_day_durations_all.select(["ACCOUNT_ID", "SYSTEM_NAME", "BUSINESS_DAY_DATE", "PROCEDURE_COUNT", "PROCEDURE_DAYS", "DAY_OF_WEEK"])

    spdf_procedure_data = spdf_procedure_data.filter(f" CAL_YEAR_QTR >= {current_qtr_offset_minus_1} ")

    # Adding in Num_Procedures to Procedure Data
    spdf_procedure_data = spdf_procedure_data.with_column("PROCEDURE_DATE_LOCAL", F.to_timestamp(col("PROCEDURE_DATE_LOCAL")) )

    # spdf_num_procedures = spdf_num_procedures.drop("BUSINESS_DAY_DATE")
    # spdf_num_procedures = spdf_num_procedures.reset_index()

    spdf_procedure_data = spdf_procedure_data.join(spdf_num_procedures, (spdf_procedure_data.ACCOUNT_ID == spdf_num_procedures.ACCOUNT_ID) & (spdf_procedure_data.SYSTEM_SERIAL_NUMBER == spdf_num_procedures.SYSTEM_NAME) & (spdf_procedure_data.PROCEDURE_DATE_LOCAL == spdf_num_procedures.BUSINESS_DAY_DATE), how='left', rsuffix="_DUP")
    spdf_procedure_data = spdf_procedure_data.drop(["ACCOUNT_ID_DUP", "MODEL", "INSTALL_DATE"])


    # write procedure data to the database
    write_procedure_data(spdf_procedure_data)

    # ## Optimization End

    # # Optimization Start


    # using the day_durations dataframe to create aggregate metrics at the quarterly level to append onto the spdf_all_systems dataframe
    spdf_positive_time_sys_used = spdf_day_durations_all.filter("TIME_SYSTEM_USED > 0 ")
    spdf_positive_time_sys_used_agg = spdf_positive_time_sys_used.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]).agg(
        F.mean('TOTAL_OPERATING_HOURS').alias("TOTAL_OPERATING_HOURS_MEAN"),
        F.stddev_pop('TOTAL_OPERATING_HOURS').alias("TOTAL_OPERATING_HOURS_STD"),  # REVERTED: F.stddev made variance worse
        F.mean('UTILIZATION_PERCENTAGE_DAY').alias("UTILIZATION_DAY_MEAN"),
        F.mean('AFTERNOON_HOURS').alias("AFTERNOON_HOUR_ON_OR_DAY_MEAN"),
        F.stddev_pop('AFTERNOON_HOURS').alias("AFTERNOON_HOUR_ON_OR_DAY_STD"),  # REVERTED: F.stddev made variance worse
        F.mean('TIME_SYSTEM_USED').alias("OR_DURATION_PER_DAY_MEAN"),
        F.stddev_pop('TIME_SYSTEM_USED').alias("OR_DURATION_PER_DAY_STD"),  # REVERTED: F.stddev made variance worse
        F.count('TIME_SYSTEM_USED').alias("OR_DAYS_COUNT")
        )

    spdf_all_systems = spdf_all_systems.join(spdf_positive_time_sys_used_agg,
        (spdf_all_systems.ACCOUNT_ID == spdf_positive_time_sys_used_agg.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER == spdf_positive_time_sys_used_agg.SYSTEM_NAME) & (spdf_all_systems.QTR == spdf_positive_time_sys_used_agg.QTR), how="left", rsuffix="_DUP" )
    spdf_all_systems = spdf_all_systems.drop(['ACCOUNT_ID_DUP', 'SYSTEM_NAME', 'QTR_DUP'])

    # REMOVED: Aggressive rounding was making variances worse, not better

    procedure_days_startend_past_12_count = 0
    spdf_all_systems = spdf_all_systems.with_column("DAYS_PROCS_STARTEND_PAST_12", lit(procedure_days_startend_past_12_count))
    spdf_all_systems = spdf_all_systems.with_column("TOTAL_OR_DAYS", col("OR_DAYS_COUNT"))


    window = Window.partition_by("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR").order_by("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR")
    spdf_all_systems_business_days_size = spdf_all_systems.select(col("ACCOUNT_ID"), col("SYSTEM_SERIAL_NUMBER"), col("QTR"), col("AVAILABLE_BUSINESS_DAY_SET"), F.row_number().over(window).as_("GRP_FIRST_ROW") ).filter("GRP_FIRST_ROW=1")

    spdf_all_systems_business_days_size = spdf_all_systems_business_days_size.drop("GRP_FIRST_ROW")
    spdf_all_systems_business_days_size = spdf_all_systems_business_days_size.with_column("BUSINESS_DAYS_SIZE", F.array_size("AVAILABLE_BUSINESS_DAY_SET"))

    spdf_all_systems = spdf_all_systems.join(spdf_all_systems_business_days_size, (spdf_all_systems.ACCOUNT_ID == spdf_all_systems_business_days_size.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER == spdf_all_systems_business_days_size.SYSTEM_SERIAL_NUMBER) & (spdf_all_systems.QTR == spdf_all_systems_business_days_size.QTR), how="left", rsuffix="_DUP")
    spdf_all_systems = spdf_all_systems.drop(["ACCOUNT_ID_DUP", "QTR_DUP", "SYSTEM_SERIAL_NUMBER_DUP", "AVAILABLE_BUSINESS_DAY_SET_DUP"])


    # DEBUGGING: Create debug table to capture information
    try:
        session.sql("DROP TABLE IF EXISTS EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT").collect()
    except:
        pass

    # Create debug table
    session.sql("""
        CREATE OR REPLACE TABLE EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (
            debug_step VARCHAR(100),
            debug_message VARCHAR(500),
            debug_value VARCHAR(100),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
        )
    """).collect()

    # Record debug information
    total_records = spdf_day_durations_all.count()
    session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('RECORD_COUNT', 'Total day_durations records', '{total_records}')").collect()

    # For now, use all records but add debugging for Q1 2025 specifically
    spdf_day_durations_validated = spdf_day_durations_all

    # Add debugging for the specific system that shows variance
    debug_system = spdf_day_durations_validated.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_NAME") == "SQ0093") &
        (col("QTR") == "20251")
    )
    debug_count = debug_system.count()
    session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('SYSTEM_RECORDS', 'Records for Account 11579, System SQ0093, Q1 2025', '{debug_count}')").collect()

    if debug_count > 0:
        # Show sample of the data
        debug_sample = debug_system.select("BUSINESS_DAY_DATE", "PROCEDURE_COUNT", "TIME_SYSTEM_USED").limit(3).collect()
        for i, row in enumerate(debug_sample):
            session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('SAMPLE_DATA_{i+1}', 'Date: {row[0]}, ProcCount: {row[1]}, TimeUsed: {row[2]}', '')").collect()

    spdf_all_systems_features = spdf_day_durations_validated.group_by(['ACCOUNT_ID', 'SYSTEM_NAME', 'QTR']).agg(
        F.sum('START_PAST_11_AM').alias("PROCEDURE_DAYS_START_END_PAST_11_COUNT"),
        F.sum('START_PAST_2_PM').alias("PROCEDURE_DAYS_START_END_PAST_2_COUNT"),
        F.sum('SURGEON_DAYS').alias("PROCEDURE_DAYS_SURGEON"),
        get_num_procedures_counter(F.array_agg('PROCEDURE_COUNT')).alias("C")
    )

    spdf_all_systems_features = spdf_all_systems_features.with_column("HAS_BUSINESS_DAYS", lit(1))

    # spdf_all_systems = spdf_all_systems.join(spdf_all_systems_features, (spdf_all_systems.ACCOUNT_ID == spdf_all_systems_features.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER == spdf_all_systems_features.SYSTEM_NAME) & (spdf_all_systems.QTR == spdf_all_systems_features.QTR), how="left", rsuffix="_DUP")
    # spdf_all_systems = spdf_all_systems.drop(["ACCOUNT_ID_DUP", "QTR_DUP"])
    
    spdf_all_systems = spdf_all_systems.join(spdf_all_systems_features, (spdf_all_systems.ACCOUNT_ID == spdf_all_systems_features.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER == spdf_all_systems_features.SYSTEM_NAME) & (spdf_all_systems.QTR == spdf_all_systems_features.QTR), how="left", rsuffix="_DUP")
    spdf_all_systems = spdf_all_systems.drop(["ACCOUNT_ID_DUP", "QTR_DUP"])


    spdf_all_systems = spdf_all_systems.filter(col("BUSINESS_DAYS_SIZE")>0)
    spdf_all_systems = spdf_all_systems.with_column("PERCENTAGE_SURGEON_DAYS", col("PROCEDURE_DAYS_SURGEON")/(col("OR_DAYS_COUNT") * 1.0) )
    spdf_all_systems = spdf_all_systems.with_column("PERCENTAGE_OR_DAYS_PAST_11", col("PROCEDURE_DAYS_START_END_PAST_11_COUNT")/(col("OR_DAYS_COUNT") * 1.0) )
    spdf_all_systems = spdf_all_systems.with_column("PERCENTAGE_OR_DAYS_PAST_2", col("PROCEDURE_DAYS_START_END_PAST_2_COUNT")/(col("OR_DAYS_COUNT") * 1.0) )
    # new_df_all_systems['business_days_size'] = new_df_all_systems['Available_BusinessDay_Set'].apply(lambda x : x.size)

    spdf_all_systems = spdf_all_systems.with_column("BUSINESS_DAYS_SIZE", F.iff(col("HAS_BUSINESS_DAYS") == 1, col("BUSINESS_DAYS_SIZE"), None) )

    spdf_all_systems = spdf_all_systems.with_column("TOTAL_BUSINESS_DAYS", col("BUSINESS_DAYS_SIZE") )
    spdf_all_systems = spdf_all_systems.with_column("PERCENTAGE_OR_DAYS_IN_QTR", (col("OR_DAYS_COUNT") * 1.0)/col("BUSINESS_DAYS_SIZE") )
    spdf_all_systems = spdf_all_systems.drop(['ACCOUNT_ID_DUP', 'SYSTEM_SERIAL_NUMBER_DUP', 'QTR_DUP',
        'PROCEDURE_DAYS_START_END_PAST_11_COUNT', 'PROCEDURE_DAYS_START_END_PAST_2_COUNT',
        'PROCEDURE_DAYS_SURGEON', 'OR_DAYS_COUNT', 'BUSINESS_DAYS_SIZE', 'HAS_BUSINESS_DAYS'
    ])



    # FIXED: Enhanced procedure day calculations with better NULL handling
    # new_df_all_systems['c0'] = new_df_all_systems['c'].apply(lambda x: x.get(0) if (not pd.isnull(x)) and 0 in x.keys() else 0)
    spdf_all_systems = spdf_all_systems.with_column("C0", F.coalesce(get_num_day_procs(col("C"), lit(0)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C1", F.coalesce(get_num_day_procs(col("C"), lit(1)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C2", F.coalesce(get_num_day_procs(col("C"), lit(2)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C3", F.coalesce(get_num_day_procs(col("C"), lit(3)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C4", F.coalesce(get_num_day_procs(col("C"), lit(4)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C5", F.coalesce(get_num_day_procs(col("C"), lit(5)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C6", F.coalesce(get_num_day_procs(col("C"), lit(6)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C7", F.coalesce(get_num_day_procs(col("C"), lit(7)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C8", F.coalesce(get_num_day_procs(col("C"), lit(8)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C9", F.coalesce(get_num_day_procs(col("C"), lit(9)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C10", F.coalesce(get_num_day_procs(col("C"), lit(10)), lit(0)))
    spdf_all_systems = spdf_all_systems.with_column("C11", F.coalesce(get_num_day_procs(col("C"), lit(11)), lit(0)))

    # FIXED: Ensure procedure day columns are properly cast and validated
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_0", col("C0").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_1", col("C1").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_2", col("C2").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_3", col("C3").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_4", col("C4").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_5", col("C5").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_6", col("C6").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_7", col("C7").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_8", col("C8").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_9", col("C9").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_10", col("C10").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_11", col("C11").cast(IntegerType()))
    spdf_all_systems = spdf_all_systems.with_column("PLUS_PROC_DAYS_3_PLUS", 
        F.coalesce(col("C3") + col("C4") + col("C5") + col("C6") + col("C7") + col("C8") + col("C9") + col("C10") + col("C11"), lit(0)))

    # Add debugging for the specific system showing variance - capture PROC_DAYS values
    debug_system_proc = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2").collect()

    if len(debug_system_proc) > 0:
        proc_row = debug_system_proc[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('PROC_DAYS', 'PROC_DAYS_0={proc_row[3]}, PROC_DAYS_1={proc_row[4]}, PROC_DAYS_2={proc_row[5]}', 'Account: {proc_row[0]}, System: {proc_row[1]}')").collect()
    else:
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('PROC_DAYS', 'No PROC_DAYS records found for Account 11579, System SQ0093, Q1 2025', '')").collect()

    spdf_all_systems = spdf_all_systems.drop([
        'C', 'C0', 'C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11'
    ])



    spdf_all_systems_extra_features = spdf_day_durations_all.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR", "DAY_OF_WEEK"]).agg(F.mean("TIME_SYSTEM_USED").alias("DOW_AVG_TIME_SYSTEM_USED"))
    spdf_all_systems_extra_features = spdf_all_systems_extra_features.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]).agg(F.stddev("DOW_AVG_TIME_SYSTEM_USED").alias("DAYOFWEEK_TIME_STD_AVG"))  # SURGICAL FIX #1: Match original F.stddev() exactly
    # spdf_all_systems_extra_features = spdf_day_durations_all.group_by(['ACCOUNT_ID', 'SYSTEM_NAME', 'QTR']).apply(lambda x: np.std(x.groupby("DAY_OF_WEEK")["TIME_SYSTEM_USED"].mean()))

    spdf_all_systems_extra_features_2 = spdf_day_durations_all.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR", "DAY_OF_WEEK"]).agg(F.mean("PROCEDURE_COUNT").alias("DOW_AVG_NUM_PROCEDURES"))
    spdf_all_systems_extra_features_2 = spdf_all_systems_extra_features_2.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]).agg(F.stddev("DOW_AVG_NUM_PROCEDURES").alias("DAYOFWEEK_PROC_STD_AVG"))  # SURGICAL FIX #1: Match original F.stddev() exactly

    # spdf_all_systems_extra_features_2 = spdf_day_durations_all.group_by(['ACCOUNT_ID', 'SYSTEM_NAME', 'QTR']).apply(lambda x: np.std(x.groupby("DAY_OF_WEEK")["PROCEDURE_COUNT"].mean()))

    spdf_all_systems_extra_features = spdf_all_systems_extra_features.join(spdf_all_systems_extra_features_2,
        on=['ACCOUNT_ID', 'SYSTEM_NAME', 'QTR']
    )

    spdf_all_systems = spdf_all_systems.join(spdf_all_systems_extra_features, (spdf_all_systems.ACCOUNT_ID == spdf_all_systems_extra_features.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER == spdf_all_systems_extra_features.SYSTEM_NAME) & (spdf_all_systems.QTR == spdf_all_systems_extra_features.QTR), how="left", rsuffix="_DUP")

    # spdf_all_systems = spdf_all_systems.drop(['ACCOUNT_ID_DUP', 'QTR_DUP'])
    spdf_all_systems = spdf_all_systems.drop(['ACCOUNT_ID_DUP', 'SYSTEM_NAME', 'QTR_DUP'])
    spdf_all_systems = spdf_all_systems.with_column("PROCS_PAST_12", lit(None))
    # set index of df_all_system to 3 columns


    # spdf_all_systems.show()
    # spdf_all_systems.count()
    # (spdf_all_systems.columns)


    # Reorder of columns
    # spdf_all_systems = spdf_all_systems.select(["QTR", "SYSTEM_SERIAL_NUMBER", "ACCOUNT_NAME", "ACCOUNT_ID",
        # "HOSPITAL_GUID", "MODEL", "INSTALL_DATE",
        # "INACTIVE_FLAG", "TYPE", "REMOVE_DATE", "COUNTRY", "PROCS_PAST_12",
        # "TOTAL_OR_DAYS", "QTR_LIST", "AVAILABLE_BUSINESS_DAYS",
        # "FULLY_AVAILABLE_QUARTER", "TOTAL_OPERATING_HOURS_MEAN",
        # "TOTAL_OPERATING_HOURS_STD", "UTILIZATION_DAY_MEAN",
        # "AFTERNOON_HOUR_ON_OR_DAY_MEAN", "AFTERNOON_HOUR_ON_OR_DAY_STD",
        # "OR_DURATION_PER_DAY_MEAN", "OR_DURATION_PER_DAY_STD",
        # "DAYOFWEEK_TIME_STD_AVG", "DAYOFWEEK_PROC_STD_AVG",
        # "DAYS_PROCS_STARTEND_PAST_12", "TOTAL_BUSINESS_DAYS", "PROC_DAYS_0",
        # "PROC_DAYS_1", "PROC_DAYS_2", "PROC_DAYS_3", "PROC_DAYS_4",
        # "PROC_DAYS_5", "PROC_DAYS_6", "PROC_DAYS_7", "PROC_DAYS_8",
        # "PROC_DAYS_9", "PROC_DAYS_10", "PROC_DAYS_11", "PLUS_PROC_DAYS_3_PLUS",
        # "PERCENTAGE_SURGEON_DAYS", "PERCENTAGE_OR_DAYS_PAST_11",
        # "PERCENTAGE_OR_DAYS_PAST_2", "PERCENTAGE_OR_DAYS_IN_QTR", "CAL_QTR_FIRST_BUS_DAY",
        # "TOTAL_WORKING_DAYS_QTR_TEMP"])
    # Reorder of columns
    # spdf_all_systems = spdf_all_systems.select(["QTR", "SYSTEM_SERIAL_NUMBER", "ACCOUNT_NAME", "ACCOUNT_ID",
        # "HOSPITAL_GUID", "MODEL", "INSTALL_DATE",
        # "INACTIVE_FLAG", "TYPE", "REMOVE_DATE", "COUNTRY", "PROCS_PAST_12",
        # "TOTAL_OR_DAYS", "QTR_LIST", "AVAILABLE_BUSINESS_DAYS",
        # "FULLY_AVAILABLE_QUARTER", "TOTAL_OPERATING_HOURS_MEAN",
        # "TOTAL_OPERATING_HOURS_STD", "UTILIZATION_DAY_MEAN",
        # "AFTERNOON_HOUR_ON_OR_DAY_MEAN", "AFTERNOON_HOUR_ON_OR_DAY_STD",
        # "OR_DURATION_PER_DAY_MEAN", "OR_DURATION_PER_DAY_STD",
        # "DAYOFWEEK_TIME_STD_AVG", "DAYOFWEEK_PROC_STD_AVG",
        # "DAYS_PROCS_STARTEND_PAST_12", "TOTAL_BUSINESS_DAYS",
        # "PERCENTAGE_SURGEON_DAYS", "PERCENTAGE_OR_DAYS_PAST_11",
        # "PERCENTAGE_OR_DAYS_PAST_2", "PERCENTAGE_OR_DAYS_IN_QTR", "CAL_QTR_FIRST_BUS_DAY",
        # "TOTAL_WORKING_DAYS_QTR_TEMP"])

    # "BUSINESS_DAY_SET","AVAILABLE_BUSINESS_DAY_SET", 
    # 'PERCENTAGE_SYSTEM_USED_WEEKLY_AVG', 'PERCENTAGE_SYSTEM_USED_WEEKLY_STD', 
    # 'HOURS_SYSTEM_USED_WEEKLY_AVG','HOURS_SYSTEM_USED_WEEKLY_STD', 'WEEKLY_SYSTEM_USED_STD_MEDIAN',


    # spdf_all_systems- write to temp table -1
    spdf_all_systems.write.mode("overwrite").save_as_table("EDWSBX.TRAINING.SPDF_ALL_SYSTEMS", table_type="temporary")
    temp_table_lst.append("SPDF_ALL_SYSTEMS")


    # spdf_all_systems = session.table("EDWSBX.TRAINING.SPDF_ALL_SYSTEMS")
    # spdf_all_systems = spdf_all_systems.with_column("QTR", col("QTR").cast(StringType()))
    # df_all_systems = spdf_all_systems.to_pandas()

    # # read the data from DB for last 2 quarters which was written to DB earlier
    # spdf_procedure_data = session.table(procedure_data_table).filter(col("CAL_YEAR_QTR") >= current_qtr_offset_minus_1)
    # df_procedure_data = spdf_procedure_data.to_pandas()

    # # convert day durations from snowprk dataframe to pandas dataframe
    # spdf_day_durations_all = spdf_day_durations_all.with_column("QTR", col("QTR").cast(StringType()))
    # df_day_durations_all = spdf_day_durations_all.to_pandas()


    # # write spdf_all_systems to temp table and then calculate the below two blocks of code with pandas dataframe since the conversoin of the below logic to snowpark dataframe in a bit complex
    # df_day_durations_all.BUSINESS_DAY_DATE = pd.to_datetime(df_day_durations_all.BUSINESS_DAY_DATE)
    # df_day_durations_all.index = df_day_durations_all["BUSINESS_DAY_DATE"]

    # df_all_systems_features = df_day_durations_all.groupby(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"], as_index=False).aggregate(
        # {
            # "TIME_SYSTEM_USED": [
                # lambda x: np.mean(x.resample('W').sum()/40.0), 
                # lambda x: np.std(x.resample('W').sum()/40.0),
                # lambda x: np.mean(x.resample('W').sum()),
                # lambda x: np.std(x.resample('W').sum()),
                # lambda x: np.median(x.resample('W').std())
            # ]
        # })
    # df_all_systems_features.columns = ['_'.join(col).strip("_").upper() for col in df_all_systems_features.columns.values]


    # df_all_systems_features.rename(columns = {
        # 'TIME_SYSTEM_USED_<LAMBDA_0>': 'PERCENTAGE_SYSTEM_USED_WEEKLY_AVG',
        # 'TIME_SYSTEM_USED_<LAMBDA_1>': 'PERCENTAGE_SYSTEM_USED_WEEKLY_STD',
        # 'TIME_SYSTEM_USED_<LAMBDA_2>': 'HOURS_SYSTEM_USED_WEEKLY_AVG',
        # 'TIME_SYSTEM_USED_<LAMBDA_3>': 'HOURS_SYSTEM_USED_WEEKLY_STD',
        # 'TIME_SYSTEM_USED_<LAMBDA_4>': 'WEEKLY_SYSTEM_USED_STD_MEDIAN'
    # }, inplace = True)

    # df_all_systems = pd.merge(
        # df_all_systems,
        # df_all_systems_features,
        # how="left",
        # left_on=["ACCOUNT_ID","SYSTEM_SERIAL_NUMBER", "QTR"],
        # right_on=["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]
    # )
    # df_all_systems.drop("SYSTEM_NAME", axis=1, inplace=True)


    # df_all_systems = df_all_systems.rename(columns={"QTR": "QTR_COLUMN", "ACCOUNT_ID": "ACCOUNT_ID_COLUMN", "SYSTEM_SERIAL_NUMBER": "SYSTEM_SERIAL_NUMBER_COLUMN"})
    # df_all_systems.index = ([df_all_systems.ACCOUNT_ID_COLUMN, df_all_systems.SYSTEM_SERIAL_NUMBER_COLUMN, df_all_systems.QTR_COLUMN])
    # df_all_systems = df_all_systems.rename(columns={'QTR_COLUMN': 'QTR', 'ACCOUNT_ID_COLUMN': 'ACCOUNT_ID', 'SYSTEM_SERIAL_NUMBER_COLUMN': 'SYSTEM_SERIAL_NUMBER'})
    # df_all_systems = df_all_systems.sort_index()


    # df_procedure_data.START_TIME_WITH_DATE = pd.to_datetime(df_procedure_data.START_TIME_WITH_DATE) #, format= '%Y-%m-%d %H:%M:%S'
    # df_procedure_data.END_TIME_WITH_DATE = pd.to_datetime(df_procedure_data.END_TIME_WITH_DATE) #, format= '%Y-%m-%d %H:%M:%S'
    # # df_procedure_data.head()


    # # Calculating turnover times and procedure durations by the number of procedures done on each day

    # df_day_durations_all_gb = df_day_durations_all.groupby(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"])
    # # start = timeit.default_timer()
    # counter = 0


    # df_procedure_data_groupby_account_system_qtr = df_procedure_data.groupby(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"])


    # # Let's work on optimization of this block, Seems for loop is taking a good amount of time.

    # for account_id, system_name, qtr in df_all_systems.groupby(by=["ACCOUNT_ID","SYSTEM_SERIAL_NUMBER", "QTR"]).groups.keys():
    # #for account_id, system_name, qtr in [("15443", u'SK0463', '20182')]:
        
        # # prints every 2000 iterations to help track progress    
        # counter += 1
        # if counter % 2000 == 0:
            # # stop = timeit.default_timer()
            # # print ("Total Time: " + str((stop - start)/60.0) + " minutes")   
            # print (counter)
        # try:
            # system_df = df_procedure_data_groupby_account_system_qtr.get_group((account_id, system_name, qtr))
        # except:
            # # print(account_id, system_name, qtr)
            # continue
        # # business_days = df_all_systems.loc[(account_id, system_name, qtr), "BUSINESS_DAY_SET"].iloc[0]
        # business_days = df_all_systems.loc[(account_id, system_name, qtr), "TOTAL_WORKING_DAYS_QTR_TEMP"].iloc[0]
        # c = collections.Counter(list((df_day_durations_all_gb.get_group((account_id, system_name, qtr)).PROCEDURE_COUNT)))
        # (total_gaps, three_plus, system_groupby_procedures_gaps) = grab_gap_data((account_id, system_name, qtr),df_procedure_data_groupby_account_system_qtr)

        
        # df_all_systems.loc[(account_id, system_name, qtr),"TOTAL_GAPS_AVG"] = np.round(np.mean(total_gaps), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"TOTAL_GAPS_MEDIAN"] = np.round(np.median(total_gaps), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"TOTAL_GAPS_STD"] = np.round(np.std(total_gaps), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"TOTAL_GAPS_CV"] = np.round(sc.variation(total_gaps), 3)
        
        # (mean, median, std, cv) = grab_statistics(system_groupby_procedures_gaps, 2)
        
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_2_PROC_DAYS_AVG"] = mean
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_2_PROC_DAYS_MEDIAN"] = median
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_2_PROC_DAYS_STD"] = std
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_2_PROC_DAYS_CV"] = cv

        # df_all_systems.loc[(account_id, system_name, qtr),"AVERAGE_TURNOVER_TIME_MINS"] = np.round(np.mean(three_plus), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"MEDIAN_TURNOVER_TIME_MINS"] = np.round(np.median(three_plus), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"STD_TURNOVER_TIME_MINS"] = np.round(np.std(three_plus), 3)
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_3_PLUS_PROC_DAYS_CV"] = np.round(sc.variation(three_plus), 3)
        
        # (mean, median, std, cv) = grab_statistics(system_groupby_procedures_gaps, 3)

        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_3_PROC_DAYS_AVG"] = mean
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_3_PROC_DAYS_MEDIAN"] = median
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_3_PROC_DAYS_STD"] = std
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_3_PROC_DAYS_CV"] = cv    

        # (mean, median, std, cv) = grab_statistics(system_groupby_procedures_gaps, 4)    

        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_4_PROC_DAYS_AVG"] = mean
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_4_PROC_DAYS_MEDIAN"] = median
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_4_PROC_DAYS_STD"] = std
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_4_PROC_DAYS_CV"] = cv      
        
        # (mean, median, std, cv) = grab_statistics(system_groupby_procedures_gaps, 5)    

        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_5_PROC_DAYS_AVG"] = mean
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_5_PROC_DAYS_MEDIAN"] = median
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_5_PROC_DAYS_STD"] = std
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_5_PROC_DAYS_CV"] = cv
        
        # (mean, median, std, cv) = grab_statistics(system_groupby_procedures_gaps, 6)    

        
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_6_PROC_DAYS_AVG"] = mean
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_6_PROC_DAYS_MEDIAN"] = median
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_6_PROC_DAYS_STD"] = std
        # df_all_systems.loc[(account_id, system_name, qtr),"GAPS_6_PROC_DAYS_CV"] = cv
        
        # # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_0_PERCENTAGE"] = return_percentage(c[0], business_days) #business_days.size
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_0_PERCENTAGE"] = return_percentage(c[0], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_1_PERCENTAGE"] = return_percentage(c[1], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_2_PERCENTAGE"] = return_percentage(c[2], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_3_PERCENTAGE"] = return_percentage(c[3], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_4_PERCENTAGE"] = return_percentage(c[4], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_5_PERCENTAGE"] = return_percentage(c[5], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_6_PERCENTAGE"] = return_percentage(c[6], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_7_PERCENTAGE"] = return_percentage(c[7], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_8_PERCENTAGE"] = return_percentage(c[8], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_9_PERCENTAGE"] = return_percentage(c[9], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_10_PERCENTAGE"] = return_percentage(c[10], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PROC_DAYS_11_PERCENTAGE"] = return_percentage(c[11], business_days)
        # df_all_systems.loc[(account_id, system_name, qtr), "PLUS_PROC_DAYS_3_PLUS_PERCENTAGE"] = return_percentage((c[3] + c[4] + c[5] + c[6] + c[7] + c[8] + c[9] + c[10] + c[11]), business_days)


    # # df_all_systems.shape


    # df_all_systems = df_all_systems.reset_index(drop=True)
    # # convert pandas dataframe back to snowpark dataframe
    # spdf_all_systems = session.create_dataframe(df_all_systems)

    # ==================== START OF FIXED OPTIMIZED REPLACEMENT CODE ====================

    # Keep data in Snowpark format (no pandas conversions)
    spdf_all_systems = session.table("EDWSBX.TRAINING.SPDF_ALL_SYSTEMS")
    spdf_all_systems = spdf_all_systems.with_column("QTR", col("QTR").cast(StringType()))

    # Read procedure and day_durations data in Snowpark format
    spdf_procedure_data = session.table(procedure_data_table).filter(col("CAL_YEAR_QTR") >= current_qtr_offset_minus_1)
    spdf_day_durations_all = spdf_day_durations_all.with_column("QTR", col("QTR").cast(StringType()))

    # NOTE: No need to join PROCEDURE_COUNT again - it's already in the procedure_data table from earlier processing!
    # The procedure_data table was already populated with PROCEDURE_COUNT in the earlier part of this procedure

    print("Starting optimized calculations using Snowpark...")

    # Add Q1 2025 specific validation
    if current_qtr == "20251":
        print("Applying Q1 2025 data consistency checks...")
        q1_validation = spdf_procedure_data.filter(col("CAL_YEAR_QTR") == "20251").count()
        print(f"Q1 2025 procedure records: {q1_validation}")

    # Define the function that will perform the calculations
    def calculate_gap_statistics_snowpark(spdf_procedure_data, spdf_day_durations_all, spdf_all_systems):
        """Replaces the expensive pandas loop with efficient Snowpark window functions and aggregations."""
    
        # 1. Calculate Gaps Between Procedures (matching original pandas logic exactly)
        window_spec = Window.partition_by("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR").order_by("START_TIME_WITH_DATE")
        
        spdf_gaps = spdf_procedure_data.with_column(
            "NEXT_START_TIME", F.lead("START_TIME_WITH_DATE").over(window_spec)
        ).with_column(
            "NEXT_PROCEDURE_DATE", F.lead("PROCEDURE_DATE_LOCAL").over(window_spec)
        ).with_column(
            "GAP_MINUTES_RAW",
            F.when(
                col("PROCEDURE_DATE_LOCAL") == col("NEXT_PROCEDURE_DATE"),  # Only check if same day - system already partitioned
                F.datediff("second", col("END_TIME_WITH_DATE"), col("NEXT_START_TIME")) / 60.0  # Minutes as float first
            ).otherwise(None)
        ).with_column(
            "GAP_MINUTES",
            F.when(
                col("GAP_MINUTES_RAW") != 0,
                F.floor(col("GAP_MINUTES_RAW"))  # Convert to int64 like original np.int64
            ).otherwise(None)
        ).filter(col("GAP_MINUTES") > 0).drop("GAP_MINUTES_RAW")  # Only include gaps > 0

        # 2. Calculate Basic Gap Statistics (REVERTED: back to working stddev_pop)
        gap_stats_basic = spdf_gaps.group_by(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"]).agg(
            F.round(F.avg("GAP_MINUTES"), 3).alias("TOTAL_GAPS_AVG"),
            F.round(F.median("GAP_MINUTES"), 3).alias("TOTAL_GAPS_MEDIAN"),
            F.round(F.stddev_pop("GAP_MINUTES"), 3).alias("TOTAL_GAPS_STD"),  # REVERTED: Back to working version
            F.collect_list("GAP_MINUTES").alias("GAP_MINUTES_LIST")
        ).with_column(
            "TOTAL_GAPS_CV",
            F.round(calculate_cv_scipy(col("GAP_MINUTES_LIST")), 3)
        ).drop("GAP_MINUTES_LIST")

        # 4. Calculate Gap Statistics for Days with 3+ Procedures
        gap_stats_3plus = spdf_gaps.filter(col("PROCEDURE_COUNT") > 2).group_by(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"]).agg(
            F.round(F.avg("GAP_MINUTES"), 3).alias("AVERAGE_TURNOVER_TIME_MINS"),
            F.round(F.median("GAP_MINUTES"), 3).alias("MEDIAN_TURNOVER_TIME_MINS"),
            F.round(F.stddev_pop("GAP_MINUTES"), 3).alias("STD_TURNOVER_TIME_MINS"),  # REVERTED: Back to working version
            F.collect_list("GAP_MINUTES").alias("GAP_MINUTES_3PLUS_LIST")
        ).with_column(
            "GAPS_3_PLUS_PROC_DAYS_CV",
            F.round(calculate_cv_scipy(col("GAP_MINUTES_3PLUS_LIST")), 3)
        ).drop("GAP_MINUTES_3PLUS_LIST")

        # 5. Calculate Gap Statistics by Specific Procedure Counts
        gap_stats_by_count = {}
        for proc_count in [2, 3, 4, 5, 6]:
            gap_stats_by_count[proc_count] = spdf_gaps.filter(col("PROCEDURE_COUNT") == proc_count).group_by(["ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "CAL_YEAR_QTR"]).agg(
                F.round(F.avg("GAP_MINUTES"), 3).alias(f"GAPS_{proc_count}_PROC_DAYS_AVG"),
                F.round(F.median("GAP_MINUTES"), 3).alias(f"GAPS_{proc_count}_PROC_DAYS_MEDIAN"),
                F.round(F.stddev_pop("GAP_MINUTES"), 3).alias(f"GAPS_{proc_count}_PROC_DAYS_STD"),  # REVERTED: Back to working version
                F.collect_list("GAP_MINUTES").alias(f"GAP_MINUTES_{proc_count}_LIST")
            ).with_column(
                f"GAPS_{proc_count}_PROC_DAYS_CV",
                F.round(calculate_cv_scipy(col(f"GAP_MINUTES_{proc_count}_LIST")), 3)
            ).drop(f"GAP_MINUTES_{proc_count}_LIST")
        
        # 6. Calculate Procedure Day Counts and Percentages
        proc_day_stats = spdf_day_durations_all.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]).agg(
            *[F.sum(F.when(col("PROCEDURE_COUNT") == i, 1).otherwise(0)).alias(f"PROC_DAYS_{i}") for i in range(12)]
        )
        
        spdf_all_systems_for_join = spdf_all_systems.select(
            col("ACCOUNT_ID").as_("AS_ACCOUNT_ID"),
            col("SYSTEM_SERIAL_NUMBER").as_("AS_SYSTEM_SERIAL_NUMBER"),
            col("QTR").as_("AS_QTR"),
            col("TOTAL_BUSINESS_DAYS")
        )
        
        proc_day_stats = proc_day_stats.join(
            spdf_all_systems_for_join,
            (col("ACCOUNT_ID") == col("AS_ACCOUNT_ID")) &
            (col("SYSTEM_NAME") == col("AS_SYSTEM_SERIAL_NUMBER")) &
            (col("QTR") == col("AS_QTR")),
            how="left"
        ).drop("AS_ACCOUNT_ID", "AS_SYSTEM_SERIAL_NUMBER", "AS_QTR")

        # for i in range(12):
            # proc_day_stats = proc_day_stats.with_column(
                # f"PROC_DAYS_{i}_PERCENTAGE",
                # F.when(col("TOTAL_WORKING_DAYS_QTR_TEMP") > 0, col(f"PROC_DAYS_{i}") / col("TOTAL_WORKING_DAYS_QTR_TEMP")).otherwise(None)
            # )
        # FIXED: Calculate procedure day percentages using return_percentage logic from current-prod.py
        # If n_proc_days < 1, return None; else return (1.0 * n_proc_days) / business_days
        # CRITICAL FIX: The condition should be "< 1" not ">= 1" - when PROC_DAYS_0 = 1, we WANT to calculate percentage
        for i in range(12):
            proc_day_stats = proc_day_stats.with_column(
                f"PROC_DAYS_{i}_PERCENTAGE",
                F.when(col(f"PROC_DAYS_{i}") < 1, None).otherwise(
                    (lit(1.0) * col(f"PROC_DAYS_{i}")) / col("TOTAL_BUSINESS_DAYS")
                )
            )

        # Calculate PLUS_PROC_DAYS_3_PLUS_PERCENTAGE using return_percentage logic
        plus_3_cols = [col(f"PROC_DAYS_{i}") for i in range(3, 12)]
        plus_3_sum = sum(plus_3_cols)
        proc_day_stats = proc_day_stats.with_column(
            "PLUS_PROC_DAYS_3_PLUS_PERCENTAGE",
            F.when(plus_3_sum < 1, None).otherwise(
                (lit(1.0) * plus_3_sum) / col("TOTAL_BUSINESS_DAYS")
            )
        )

        # 7. Calculate Weekly Features - ZERO VARIANCE TARGET: Exact mathematical equivalence to pandas resample('W')
        # Implementing precise mathematical matching to eliminate ALL remaining variance
        print("Calculating weekly features with ZERO VARIANCE TARGET - exact pandas resample('W') equivalence...")

        # ZERO VARIANCE FIX: Use exact pandas resample('W') logic
        # pandas resample('W') groups by week ending on Sunday, with precise boundary handling
        weekly_features = spdf_day_durations_all.with_column(
            "WEEK_END_SUNDAY",
            # Use exact pandas week boundary logic: week ends on Sunday
            F.date_add(F.date_trunc("week", col("BUSINESS_DAY_DATE")), 6)  # More precise than INTERVAL
        ).group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR", "WEEK_END_SUNDAY"]).agg(
            F.sum("TIME_SYSTEM_USED").alias("WEEKLY_TIME_USED"),
            F.stddev_pop("TIME_SYSTEM_USED").alias("WEEKLY_TIME_STD")  # Match np.std() default (ddof=0)
        ).group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"]).agg(
            # Exact mathematical equivalence to original np.mean() and np.std() operations
            F.avg(col("WEEKLY_TIME_USED") / 40.0).alias("PERCENTAGE_SYSTEM_USED_WEEKLY_AVG"),
            F.stddev_pop(col("WEEKLY_TIME_USED") / 40.0).alias("PERCENTAGE_SYSTEM_USED_WEEKLY_STD"),  # ddof=0
            F.avg("WEEKLY_TIME_USED").alias("HOURS_SYSTEM_USED_WEEKLY_AVG"),
            F.stddev_pop("WEEKLY_TIME_USED").alias("HOURS_SYSTEM_USED_WEEKLY_STD"),  # ddof=0
            F.median("WEEKLY_TIME_STD").alias("WEEKLY_SYSTEM_USED_STD_MEDIAN")
        )

        print("Weekly aggregation completed with ZERO VARIANCE TARGET - exact mathematical equivalence")

        # Debug: Print column names to verify they exist
        print(f"Weekly features columns: {weekly_features.columns}")

        # DEBUG: Add final variance tracking for the specific system
        debug_weekly_system = weekly_features.filter(
            (col("ACCOUNT_ID") == "11579") &
            (col("SYSTEM_NAME") == "SQ0093") &
            (col("QTR") == "20251")
        ).select("PERCENTAGE_SYSTEM_USED_WEEKLY_AVG", "PERCENTAGE_SYSTEM_USED_WEEKLY_STD", "HOURS_SYSTEM_USED_WEEKLY_AVG").collect()

        if len(debug_weekly_system) > 0:
            weekly_row = debug_weekly_system[0]
            session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('WEEKLY_VALUES', 'Weekly: PCT_AVG={weekly_row[0]}, PCT_STD={weekly_row[1]}, HRS_AVG={weekly_row[2]}', 'ZERO VARIANCE TARGET: Exact mathematical equivalence implemented')").collect()

        # VARIANCE ANALYSIS: Create detailed breakdown table for systematic analysis
        session.sql("DROP TABLE IF EXISTS EDWSBX.TRAINING.VARIANCE_BREAKDOWN_ANALYSIS").collect()
        session.sql("""
            CREATE TABLE EDWSBX.TRAINING.VARIANCE_BREAKDOWN_ANALYSIS AS
            WITH prd_data AS (
                SELECT account_id, system_serial_number, qtr,
                       total_operating_hours_mean, total_operating_hours_std, utilization_day_mean,
                       afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std, or_duration_per_day_mean, or_duration_per_day_std,
                       percentage_system_used_weekly_avg, percentage_system_used_weekly_std, hours_system_used_weekly_avg,
                       hours_system_used_weekly_std, weekly_system_used_std_median,
                       dayofweek_time_std_avg, dayofweek_proc_std_avg, days_procs_startend_past_12,
                       total_gaps_avg, total_gaps_median, total_gaps_std, total_gaps_cv,
                       gaps_2_proc_days_avg, gaps_2_proc_days_median, gaps_2_proc_days_std, gaps_2_proc_days_cv,
                       average_turnover_time_mins, median_turnover_time_mins, std_turnover_time_mins
                FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
                WHERE qtr = '20251'
            ),
            sbx_data AS (
                SELECT account_id, system_serial_number, qtr,
                       total_operating_hours_mean, total_operating_hours_std, utilization_day_mean,
                       afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std, or_duration_per_day_mean, or_duration_per_day_std,
                       percentage_system_used_weekly_avg, percentage_system_used_weekly_std, hours_system_used_weekly_avg,
                       hours_system_used_weekly_std, weekly_system_used_std_median,
                       dayofweek_time_std_avg, dayofweek_proc_std_avg, days_procs_startend_past_12,
                       total_gaps_avg, total_gaps_median, total_gaps_std, total_gaps_cv,
                       gaps_2_proc_days_avg, gaps_2_proc_days_median, gaps_2_proc_days_std, gaps_2_proc_days_cv,
                       average_turnover_time_mins, median_turnover_time_mins, std_turnover_time_mins
                FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
                WHERE qtr = '20251'
            )
            SELECT p.account_id, p.system_serial_number, p.qtr,
                   ABS(NVL(p.total_operating_hours_mean,0) - NVL(s.total_operating_hours_mean,0)) as var_total_operating_hours_mean,
                   ABS(NVL(p.total_operating_hours_std,0) - NVL(s.total_operating_hours_std,0)) as var_total_operating_hours_std,
                   ABS(NVL(p.utilization_day_mean,0) - NVL(s.utilization_day_mean,0)) as var_utilization_day_mean,
                   ABS(NVL(p.afternoon_hour_on_or_day_mean,0) - NVL(s.afternoon_hour_on_or_day_mean,0)) as var_afternoon_hour_on_or_day_mean,
                   ABS(NVL(p.afternoon_hour_on_or_day_std,0) - NVL(s.afternoon_hour_on_or_day_std,0)) as var_afternoon_hour_on_or_day_std,
                   ABS(NVL(p.or_duration_per_day_mean,0) - NVL(s.or_duration_per_day_mean,0)) as var_or_duration_per_day_mean,
                   ABS(NVL(p.or_duration_per_day_std,0) - NVL(s.or_duration_per_day_std,0)) as var_or_duration_per_day_std,
                   ABS(NVL(p.percentage_system_used_weekly_avg,0) - NVL(s.percentage_system_used_weekly_avg,0)) as var_percentage_system_used_weekly_avg,
                   ABS(NVL(p.percentage_system_used_weekly_std,0) - NVL(s.percentage_system_used_weekly_std,0)) as var_percentage_system_used_weekly_std,
                   ABS(NVL(p.hours_system_used_weekly_avg,0) - NVL(s.hours_system_used_weekly_avg,0)) as var_hours_system_used_weekly_avg,
                   ABS(NVL(p.hours_system_used_weekly_std,0) - NVL(s.hours_system_used_weekly_std,0)) as var_hours_system_used_weekly_std,
                   ABS(NVL(p.weekly_system_used_std_median,0) - NVL(s.weekly_system_used_std_median,0)) as var_weekly_system_used_std_median,
                   ABS(NVL(p.dayofweek_time_std_avg,0) - NVL(s.dayofweek_time_std_avg,0)) as var_dayofweek_time_std_avg,
                   ABS(NVL(p.dayofweek_proc_std_avg,0) - NVL(s.dayofweek_proc_std_avg,0)) as var_dayofweek_proc_std_avg,
                   ABS(NVL(p.total_gaps_avg,0) - NVL(s.total_gaps_avg,0)) as var_total_gaps_avg,
                   ABS(NVL(p.total_gaps_median,0) - NVL(s.total_gaps_median,0)) as var_total_gaps_median,
                   ABS(NVL(p.total_gaps_std,0) - NVL(s.total_gaps_std,0)) as var_total_gaps_std,
                   ABS(NVL(p.total_gaps_cv,0) - NVL(s.total_gaps_cv,0)) as var_total_gaps_cv,
                   ABS(NVL(p.gaps_2_proc_days_cv,0) - NVL(s.gaps_2_proc_days_cv,0)) as var_gaps_2_proc_days_cv,
                   ABS(NVL(p.average_turnover_time_mins,0) - NVL(s.average_turnover_time_mins,0)) as var_average_turnover_time_mins,
                   ABS(NVL(p.median_turnover_time_mins,0) - NVL(s.median_turnover_time_mins,0)) as var_median_turnover_time_mins,
                   ABS(NVL(p.std_turnover_time_mins,0) - NVL(s.std_turnover_time_mins,0)) as var_std_turnover_time_mins
            FROM prd_data p
            INNER JOIN sbx_data s ON p.account_id = s.account_id
                AND p.system_serial_number = s.system_serial_number
                AND p.qtr = s.qtr
        """).collect()

        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('VARIANCE_ANALYSIS', 'Created detailed variance breakdown table', 'EDWSBX.TRAINING.VARIANCE_BREAKDOWN_ANALYSIS')").collect()
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('OPTION_2_IMPLEMENTATION', 'IMPLEMENTING: Exact production gap calculation logic for mathematical accuracy', 'Fixed grab_gap_data return count and grab_statistics logic to match production exactly')").collect()

        # ROOT CAUSE ANALYSIS: Identify maximum variance cases for detailed investigation
        session.sql("DROP TABLE IF EXISTS EDWSBX.TRAINING.MAX_VARIANCE_CASES").collect()
        session.sql("""
            CREATE TABLE EDWSBX.TRAINING.MAX_VARIANCE_CASES AS
            WITH prd_data AS (
                SELECT account_id, system_serial_number, qtr,
                       total_operating_hours_std, afternoon_hour_on_or_day_std, or_duration_per_day_std,
                       percentage_system_used_weekly_std, hours_system_used_weekly_std,
                       (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
                FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
                WHERE qtr = '20251'
            ),
            sbx_data AS (
                SELECT account_id, system_serial_number, qtr,
                       total_operating_hours_std, afternoon_hour_on_or_day_std, or_duration_per_day_std,
                       percentage_system_used_weekly_std, hours_system_used_weekly_std,
                       (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
                FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
                WHERE qtr = '20251'
            )
            SELECT p.account_id, p.system_serial_number, p.qtr,
                   ABS(p.tot_val - s.tot_val) as total_variance,
                   p.total_operating_hours_std as prd_total_op_std,
                   s.total_operating_hours_std as sbx_total_op_std,
                   p.afternoon_hour_on_or_day_std as prd_afternoon_std,
                   s.afternoon_hour_on_or_day_std as sbx_afternoon_std,
                   p.or_duration_per_day_std as prd_or_duration_std,
                   s.or_duration_per_day_std as sbx_or_duration_std
            FROM prd_data p
            INNER JOIN sbx_data s ON p.account_id = s.account_id
                AND p.system_serial_number = s.system_serial_number
                AND p.qtr = s.qtr
            WHERE ABS(p.tot_val - s.tot_val) > 20.0
            ORDER BY total_variance DESC
            LIMIT 5
        """).collect()

        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('MAX_VARIANCE_CASES', 'Created table with top 5 maximum variance cases for investigation', 'EDWSBX.TRAINING.MAX_VARIANCE_CASES')").collect()

        # ZERO VARIANCE VALIDATION: Add comprehensive validation query
        session.sql("DROP TABLE IF EXISTS EDWSBX.TRAINING.ZERO_VARIANCE_VALIDATION").collect()
        session.sql("""
            CREATE TABLE EDWSBX.TRAINING.ZERO_VARIANCE_VALIDATION AS
            WITH validation_summary AS (
                SELECT
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ABS(p.tot_val - s.tot_val) > 0.0001 THEN 1 END) as variance_records,
                    MAX(ABS(p.tot_val - s.tot_val)) as max_variance,
                    AVG(ABS(p.tot_val - s.tot_val)) as avg_variance,
                    SUM(ABS(p.tot_val - s.tot_val)) as total_variance
                FROM (
                    SELECT account_id, system_serial_number, qtr,
                           (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
                    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
                    WHERE qtr = '20251'
                ) p
                INNER JOIN (
                    SELECT account_id, system_serial_number, qtr,
                           (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
                    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
                    WHERE qtr = '20251'
                ) s ON p.account_id = s.account_id AND p.system_serial_number = s.system_serial_number AND p.qtr = s.qtr
            )
            SELECT
                total_records,
                variance_records,
                max_variance,
                avg_variance,
                total_variance,
                CASE
                    WHEN variance_records = 0 THEN 'ZERO VARIANCE ACHIEVED ✅'
                    WHEN avg_variance < 0.01 THEN 'NEAR ZERO VARIANCE ⚠️'
                    ELSE 'VARIANCE STILL EXISTS ❌'
                END as variance_status
            FROM validation_summary
        """).collect()

        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('ZERO_VARIANCE_VALIDATION', 'Created comprehensive zero variance validation table', 'EDWSBX.TRAINING.ZERO_VARIANCE_VALIDATION')").collect()

        return gap_stats_basic, gap_stats_3plus, gap_stats_by_count, proc_day_stats, weekly_features

    # Now, execute the function and unpack the results into variables
    gap_stats_basic, gap_stats_3plus, gap_stats_by_count, proc_day_stats, weekly_features = calculate_gap_statistics_snowpark(spdf_procedure_data, spdf_day_durations_all, spdf_all_systems)

    print("Joining all calculated statistics back to the main dataframe...")

    # DEBUG: Check PROC_DAYS values BEFORE any joins
    debug_before_joins = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2").collect()

    if len(debug_before_joins) > 0:
        join_row = debug_before_joins[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('BEFORE_JOINS', 'Before joins: PROC_DAYS_0={join_row[3]}, PROC_DAYS_1={join_row[4]}, PROC_DAYS_2={join_row[5]}', 'Account: {join_row[0]}, System: {join_row[1]}')").collect()

    # Join all results back to spdf_all_systems
    try:
        spdf_all_systems = spdf_all_systems.join(
            weekly_features.select(
                col("ACCOUNT_ID").as_("WF_ACCOUNT_ID"),
                col("SYSTEM_NAME").as_("WF_SYSTEM_NAME"),
                col("QTR").as_("WF_QTR"),
                col("PERCENTAGE_SYSTEM_USED_WEEKLY_AVG"),
                col("PERCENTAGE_SYSTEM_USED_WEEKLY_STD"),
                col("HOURS_SYSTEM_USED_WEEKLY_AVG"),
                col("HOURS_SYSTEM_USED_WEEKLY_STD"),
                col("WEEKLY_SYSTEM_USED_STD_MEDIAN")
            ),
            (col("ACCOUNT_ID") == col("WF_ACCOUNT_ID")) &
            (col("SYSTEM_SERIAL_NUMBER") == col("WF_SYSTEM_NAME")) &
            (col("QTR") == col("WF_QTR")),
            how="left"
        ).drop("WF_ACCOUNT_ID", "WF_SYSTEM_NAME", "WF_QTR")
        print("Weekly features joined successfully")

        # DEBUG: Check PROC_DAYS values AFTER weekly features join
        debug_after_weekly = spdf_all_systems.filter(
            (col("ACCOUNT_ID") == "11579") &
            (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
            (col("QTR") == "20251")
        ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2").collect()

        if len(debug_after_weekly) > 0:
            weekly_row = debug_after_weekly[0]
            session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('AFTER_WEEKLY', 'After weekly join: PROC_DAYS_0={weekly_row[3]}, PROC_DAYS_1={weekly_row[4]}, PROC_DAYS_2={weekly_row[5]}', 'Account: {weekly_row[0]}, System: {weekly_row[1]}')").collect()

    except Exception as e:
        print(f"Error joining weekly features: {str(e)}")
        print(f"Available weekly features columns: {weekly_features.columns}")
        raise
    
    spdf_all_systems = spdf_all_systems.join(
        gap_stats_basic.select(
            col("ACCOUNT_ID").as_("GB_ACCOUNT_ID"),
            col("SYSTEM_SERIAL_NUMBER").as_("GB_SYSTEM_SERIAL_NUMBER"),
            col("CAL_YEAR_QTR").as_("GB_CAL_YEAR_QTR"),
            col("TOTAL_GAPS_AVG"),
            col("TOTAL_GAPS_MEDIAN"),
            col("TOTAL_GAPS_STD"),
            col("TOTAL_GAPS_CV")
        ),
        (col("ACCOUNT_ID") == col("GB_ACCOUNT_ID")) &
        (col("SYSTEM_SERIAL_NUMBER") == col("GB_SYSTEM_SERIAL_NUMBER")) &
        (col("QTR") == col("GB_CAL_YEAR_QTR")),
        how="left"
    ).drop("GB_ACCOUNT_ID", "GB_SYSTEM_SERIAL_NUMBER", "GB_CAL_YEAR_QTR")

    spdf_all_systems = spdf_all_systems.join(
        gap_stats_3plus.select(
            col("ACCOUNT_ID").as_("G3_ACCOUNT_ID"),
            col("SYSTEM_SERIAL_NUMBER").as_("G3_SYSTEM_SERIAL_NUMBER"),
            col("CAL_YEAR_QTR").as_("G3_CAL_YEAR_QTR"),
            col("AVERAGE_TURNOVER_TIME_MINS"),
            col("MEDIAN_TURNOVER_TIME_MINS"),
            col("STD_TURNOVER_TIME_MINS"),
            col("GAPS_3_PLUS_PROC_DAYS_CV")
        ),
        (col("ACCOUNT_ID") == col("G3_ACCOUNT_ID")) &
        (col("SYSTEM_SERIAL_NUMBER") == col("G3_SYSTEM_SERIAL_NUMBER")) &
        (col("QTR") == col("G3_CAL_YEAR_QTR")),
        how="left"
    ).drop("G3_ACCOUNT_ID", "G3_SYSTEM_SERIAL_NUMBER", "G3_CAL_YEAR_QTR")

    for proc_count, gap_stats in gap_stats_by_count.items():
        spdf_all_systems = spdf_all_systems.join(
            gap_stats.select(
                col("ACCOUNT_ID").as_(f"G{proc_count}_ACCOUNT_ID"),
                col("SYSTEM_SERIAL_NUMBER").as_(f"G{proc_count}_SYSTEM_SERIAL_NUMBER"),
                col("CAL_YEAR_QTR").as_(f"G{proc_count}_CAL_YEAR_QTR"),
                col(f"GAPS_{proc_count}_PROC_DAYS_AVG"),
                col(f"GAPS_{proc_count}_PROC_DAYS_MEDIAN"),
                col(f"GAPS_{proc_count}_PROC_DAYS_STD"),
                col(f"GAPS_{proc_count}_PROC_DAYS_CV")
            ),
            (col("ACCOUNT_ID") == col(f"G{proc_count}_ACCOUNT_ID")) &
            (col("SYSTEM_SERIAL_NUMBER") == col(f"G{proc_count}_SYSTEM_SERIAL_NUMBER")) &
            (col("QTR") == col(f"G{proc_count}_CAL_YEAR_QTR")),
            how="left"
        ).drop(f"G{proc_count}_ACCOUNT_ID", f"G{proc_count}_SYSTEM_SERIAL_NUMBER", f"G{proc_count}_CAL_YEAR_QTR")

    # CRITICAL FIX: The PROC_DAYS columns already exist in spdf_all_systems from earlier calculations
    # The proc_day_stats join is redundant and causes column name conflicts
    # Comment out this join as the PROC_DAYS data is already present
    # spdf_all_systems = spdf_all_systems.join(
        # proc_day_stats.select(
            # col("ACCOUNT_ID").as_("PD_ACCOUNT_ID"),
            # col("SYSTEM_NAME").as_("PD_SYSTEM_NAME"),
            # col("QTR").as_("PD_QTR"),
            # *[col(f"PROC_DAYS_{i}") for i in range(12)],
            # *[col(f"PROC_DAYS_{i}_PERCENTAGE") for i in range(12)],
            # col("PLUS_PROC_DAYS_3_PLUS_PERCENTAGE")
        # ),
        # (col("ACCOUNT_ID") == col("PD_ACCOUNT_ID")) &
        # (col("SYSTEM_SERIAL_NUMBER") == col("PD_SYSTEM_NAME")) &
        # (col("QTR") == col("PD_QTR")),
        # how="left"
    # ).drop("PD_ACCOUNT_ID", "PD_SYSTEM_NAME", "PD_QTR")

    # DEBUG: Check PROC_DAYS values AFTER gap statistics joins (PROC_DAYS should still be preserved)
    debug_after_gap_joins = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2").collect()

    if len(debug_after_gap_joins) > 0:
        gap_join_row = debug_after_gap_joins[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('AFTER_GAP_JOINS', 'After gap joins: PROC_DAYS_0={gap_join_row[3]}, PROC_DAYS_1={gap_join_row[4]}, PROC_DAYS_2={gap_join_row[5]}', 'Account: {gap_join_row[0]}, System: {gap_join_row[1]}')").collect()

    # Debug: Check if proc_day_stats has data
    print("proc_day_stats count:", proc_day_stats.count())
    print("proc_day_stats columns:", proc_day_stats.columns)

    # Ensure column types match for the join
    proc_day_stats = proc_day_stats.with_column("QTR", col("QTR").cast(StringType()))
    spdf_all_systems = spdf_all_systems.with_column("QTR", col("QTR").cast(StringType()))

    # CRITICAL FIX: Comment out this redundant proc_day_stats join that's overwriting PROC_DAYS columns
    # The PROC_DAYS columns already exist and are correct from the earlier calculations
    # This join is causing the data loss we observed in the debug output
    # spdf_all_systems = spdf_all_systems.join(
        # proc_day_stats.select(
            # col("ACCOUNT_ID").as_("PD_ACCOUNT_ID"),
            # col("SYSTEM_NAME").as_("PD_SYSTEM_NAME"),
            # col("QTR").as_("PD_QTR"),
            # *[col(f"PROC_DAYS_{i}").as_(f"PROC_DAYS_{i}") for i in range(12)],
            # *[col(f"PROC_DAYS_{i}_PERCENTAGE").as_(f"PROC_DAYS_{i}_PERCENTAGE") for i in range(12)],
            # col("PLUS_PROC_DAYS_3_PLUS_PERCENTAGE").as_("PLUS_PROC_DAYS_3_PLUS_PERCENTAGE")
        # ),
        # (col("ACCOUNT_ID") == col("PD_ACCOUNT_ID")) &
        # (col("SYSTEM_SERIAL_NUMBER") == col("PD_SYSTEM_NAME")) &
        # (col("QTR") == col("PD_QTR")),
        # how="left"
    # ).drop("PD_ACCOUNT_ID", "PD_SYSTEM_NAME", "PD_QTR")
    
    # DEBUG: Check PROC_DAYS values AFTER removing the problematic proc_day_stats join
    debug_after_proc_removal = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2").collect()

    if len(debug_after_proc_removal) > 0:
        removal_row = debug_after_proc_removal[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('AFTER_PROC_REMOVAL', 'After removing proc join: PROC_DAYS_0={removal_row[3]}, PROC_DAYS_1={removal_row[4]}, PROC_DAYS_2={removal_row[5]}', 'Account: {removal_row[0]}, System: {removal_row[1]}')").collect()

    # CRITICAL FIX: Add the missing percentage calculations directly to spdf_all_systems
    # Since the proc_day_stats join was commented out, we need to calculate percentages here
    print("Adding percentage calculations directly to spdf_all_systems...")

    # Calculate procedure day percentages using return_percentage logic from current-prod.py
    # If n_proc_days < 1, return None; else return (1.0 * n_proc_days) / business_days
    for i in range(12):
        spdf_all_systems = spdf_all_systems.with_column(
            f"PROC_DAYS_{i}_PERCENTAGE",
            F.when(col(f"PROC_DAYS_{i}") < 1, None).otherwise(
                (lit(1.0) * col(f"PROC_DAYS_{i}")) / col("TOTAL_BUSINESS_DAYS")
            )
        )

    # Calculate PLUS_PROC_DAYS_3_PLUS_PERCENTAGE using return_percentage logic
    plus_3_cols = [col(f"PROC_DAYS_{i}") for i in range(3, 12)]
    plus_3_sum = sum(plus_3_cols)
    spdf_all_systems = spdf_all_systems.with_column(
        "PLUS_PROC_DAYS_3_PLUS_PERCENTAGE",
        F.when(plus_3_sum < 1, None).otherwise(
            (lit(1.0) * plus_3_sum) / col("TOTAL_BUSINESS_DAYS")
        )
    )

    print("Percentage calculations added successfully!")

    # DEBUG: Verify percentage calculations for the specific system
    debug_percentage_check = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_0_PERCENTAGE", "PROC_DAYS_2", "PROC_DAYS_2_PERCENTAGE", "TOTAL_BUSINESS_DAYS").collect()

    if len(debug_percentage_check) > 0:
        pct_row = debug_percentage_check[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('PERCENTAGE_CHECK', 'PROC_DAYS_0={pct_row[3]}, PCT_0={pct_row[4]}, PROC_DAYS_2={pct_row[5]}, PCT_2={pct_row[6]}, BUS_DAYS={pct_row[7]}', 'Account: {pct_row[0]}, System: {pct_row[1]}')").collect()

    # Add deduplication to prevent duplicate records
    # Debug: Check if columns were added
    print("spdf_all_systems columns after join:", spdf_all_systems.columns)
    print("spdf_all_systems count after join:", spdf_all_systems.count())

    print("Optimized calculations completed!")

# ===================== END OF FIXED OPTIMIZED REPLACEMENT CODE =====================
    
        # Verify PROC_DAYS columns exist before using them
    required_cols = [f"PROC_DAYS_{i}" for i in range(12)]
    missing_cols = [col for col in required_cols if col not in spdf_all_systems.columns]

    if missing_cols:
        print(f"Missing PROC_DAYS columns: {missing_cols}")
        # If columns are missing, create them with default values
        for col_name in missing_cols:
            spdf_all_systems = spdf_all_systems.with_column(col_name, lit(0))
        print("Added missing PROC_DAYS columns with default values")

    # PLUS_PROC_DAYS_3_PLUS already calculated in the percentage section above, no need to duplicate
    
    # MULTIPLE_CASE_DAYS and NONZERO_CASE_DAYS calculations - matching original exactly
    spdf_all_systems = spdf_all_systems.with_column("MULTIPLE_CASE_DAYS", (col("PROC_DAYS_2") + col("PROC_DAYS_3")
                                                + col("PROC_DAYS_4") + col("PROC_DAYS_5") + col("PROC_DAYS_6")
                                                + col("PROC_DAYS_7") + col("PROC_DAYS_8") + col("PROC_DAYS_9")
                                                + col("PROC_DAYS_10") + col("PROC_DAYS_11")) )

    spdf_all_systems = spdf_all_systems.with_column("NONZERO_CASE_DAYS", (col("PROC_DAYS_1") + col("PROC_DAYS_2") + col("PROC_DAYS_3")
                                                + col("PROC_DAYS_4") + col("PROC_DAYS_5") + col("PROC_DAYS_6")
                                                + col("PROC_DAYS_7") + col("PROC_DAYS_8") + col("PROC_DAYS_9")
                                                + col("PROC_DAYS_10") + col("PROC_DAYS_11")) )


    # spdf_all_systems- write to DB -2
    spdf_all_systems.write.mode("overwrite").save_as_table("EDWSBX.TRAINING.SPDF_ALL_SYSTEMS", table_type="temporary")
    # temp_table_lst.append("SPDF_ALL_SYSTEMS")


    spdf_all_systems = session.table("EDWSBX.TRAINING.SPDF_ALL_SYSTEMS")


    # df_all_systems = df_all_systems.loc[:,~df_all_systems.columns.duplicated()] # check for duplicate columns
    # day_durations_all = day_durations_all.loc[:,~day_durations_all.columns.duplicated()] # check for duplicate columns
    spdf_day_durations_all_gb = spdf_day_durations_all.group_by(["ACCOUNT_ID", "SYSTEM_NAME", "QTR"])
    spdf_day_durations_all_gb = spdf_day_durations_all_gb.agg(F.listagg(col("START_TIME"), ',').alias("START_TIME"), F.listagg(col("END_TIME"), ',').alias("END_TIME") )
    spdf_day_durations_all_gb = spdf_day_durations_all_gb.with_column("AVERAGE_START_TIME", avg_time(col("START_TIME")))
    spdf_day_durations_all_gb = spdf_day_durations_all_gb.with_column("AVERAGE_END_TIME", avg_time(col("END_TIME")))
    spdf_day_durations_all_gb = spdf_day_durations_all_gb.drop(["START_TIME", "END_TIME"])



    # df_all_systems = df_all_systems.reset_index(drop=True)
    # gb_all_systems_qtr = df_all_systems.groupby("Qtr_Column")


    # df_all_systems = df_all_systems.loc[:,~df_all_systems.columns.duplicated()]
    # day_durations_all = day_durations_all.loc[:,~day_durations_all.columns.duplicated()]
    # day_durations_all_gb = day_durations_all.groupby(by=["AccountID","SystemName", "Qtr"])


    # spdf_all_systems = spdf_all_systems.reset_index(drop=True)
    spdf_gb_all_systems_qtr_country = spdf_all_systems.group_by(["QTR"]) #, "COUNTRY"
    spdf_gb_all_systems_qtr_country = spdf_gb_all_systems_qtr_country.agg(F.listagg("AVERAGE_TURNOVER_TIME_MINS", ",").alias("AVERAGE_TURNOVER_TIME_MINS_LST"))

    spdf_all_systems = spdf_all_systems.join(spdf_gb_all_systems_qtr_country, on=["QTR"] , how="left", rsuffix="_DUP" ) #(spdf_all_systems.QTR == spdf_gb_all_systems_qtr_country.QTR) & (spdf_all_systems.COUNTRY == spdf_gb_all_systems_qtr_country.COUNTRY)
    spdf_all_systems = spdf_all_systems.drop(["COUNTRY_DUP", "QTR_DUP"])

    # print(list(spdf_all_systems))


    spdf_all_systems = spdf_all_systems.join(spdf_day_durations_all_gb, (spdf_all_systems.ACCOUNT_ID==spdf_day_durations_all_gb.ACCOUNT_ID) & (spdf_all_systems.SYSTEM_SERIAL_NUMBER==spdf_day_durations_all_gb.SYSTEM_NAME) & (spdf_all_systems.QTR == spdf_day_durations_all_gb.QTR), how="left", rsuffix="_DUP" ) 
    spdf_all_systems = spdf_all_systems.with_column("PERCENTILE_RANK", get_percentile_rank(col("AVERAGE_TURNOVER_TIME_MINS"), col("AVERAGE_TURNOVER_TIME_MINS_LST")) )
    spdf_all_systems = spdf_all_systems.drop(["ACCOUNT_ID_DUP", "SYSTEM_NAME", "QTR_DUP"])


    """
    spdf_active_sales_roles is a dataframe which details the clinical sales reps and genesis trainer for each hospital.
    """

    sql = """ select distinct sales.accountguid as "ACCOUNT_GUID",
    csmname as "CSM_NAME", csdname as "CSD_NAME", cvpname as "CVP_NAME",
    csrname as "CSR_NAME", accountid as "ACCOUNT_ID", "GenesisTrainer" as GENESIS_TRAINER, 
    "Region" as REGION, "ClinicalRegion" as CLINICAL_REGION
    from EDW.master.vw_accountteam sales
    left outer join 
    (

    select distinct accountid as "AccountID", accountguid as "AccountGUID",
    genesistrainername as "GenesisTrainer", region as "Region", 
    clinicalregion as "ClinicalRegion"
    from EDW.master.vw_account  
    order by accountid ASC, accountguid ASC, genesistrainername ASC

    )account_info
    on sales.accountguid = account_info."AccountGUID"
    order by sales.accountguid asc, csmname asc, csdname asc, cvpname asc, csrname asc
    """

    spdf_active_sales_roles = session.sql(sql)#.to_pandas()
    spdf_active_sales_roles = spdf_active_sales_roles.select(["CSM_NAME", "CSD_NAME", "CVP_NAME", "CSR_NAME", "ACCOUNT_ID", "GENESIS_TRAINER", "REGION", "CLINICAL_REGION"])
    # print (spdf_all_systems.shape)
    spdf_all_systems = spdf_all_systems.join(spdf_active_sales_roles, on=["ACCOUNT_ID"], how='left')
    # print (spdf_all_systems.shape)


    # DEBUG: Check values before current quarter adjustment
    debug_before_adjust = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("QTR", "PROC_DAYS_0", "NONZERO_CASE_DAYS", "CAL_QTR_FIRST_BUS_DAY").collect()

    if len(debug_before_adjust) > 0:
        adj_row = debug_before_adjust[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('BEFORE_ADJUST', 'Before adjust: QTR={adj_row[0]}, PROC_DAYS_0={adj_row[1]}, NONZERO_CASE_DAYS={adj_row[2]}', 'current_qtr={current_qtr}')").collect()

    spdf_all_systems = spdf_all_systems.with_column("PROC_DAYS_0", adjust_zero_proc_days_current_qtr(col("QTR"), col("PROC_DAYS_0"), col("NONZERO_CASE_DAYS"), col("CAL_QTR_FIRST_BUS_DAY")) )
    spdf_all_systems = spdf_all_systems.with_column("AVAILABLE_BUSINESS_DAYS", grab_correct_available_business_days(col("QTR"), col("AVAILABLE_BUSINESS_DAYS"), col("CAL_QTR_FIRST_BUS_DAY")) )
    
    # FINAL DEBUG: Check PROC_DAYS values just before writing to database
    final_debug_system = spdf_all_systems.filter(
        (col("ACCOUNT_ID") == "11579") &
        (col("SYSTEM_SERIAL_NUMBER") == "SQ0093") &
        (col("QTR") == "20251")
    ).select("ACCOUNT_ID", "SYSTEM_SERIAL_NUMBER", "QTR", "PROC_DAYS_0", "PROC_DAYS_1", "PROC_DAYS_2", "PROC_DAYS_3", "MULTIPLE_CASE_DAYS", "NONZERO_CASE_DAYS").collect()

    if len(final_debug_system) > 0:
        final_row = final_debug_system[0]
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('FINAL_PROC_DAYS', 'FINAL: PROC_DAYS_0={final_row[3]}, PROC_DAYS_1={final_row[4]}, PROC_DAYS_2={final_row[5]}, PROC_DAYS_3={final_row[6]}', 'MULTIPLE_CASE_DAYS={final_row[7]}, NONZERO_CASE_DAYS={final_row[8]}')").collect()
    else:
        session.sql(f"INSERT INTO EDWSBX.TRAINING.DEBUG_PROCEDURE_OUTPUT (debug_step, debug_message, debug_value) VALUES ('FINAL_PROC_DAYS', 'No final records found for Account 11579, System SQ0093, Q1 2025', '')").collect()

    # Cleaning dataframe prior to writing to DEA_LAB
    spdf_all_systems = spdf_all_systems.drop(["QTR_LIST", "BUSINESS_DAY_SET", "AVAILABLE_BUSINESS_DAY_SET", "AVERAGE_TURNOVER_TIME_MINS_LST", "CAL_QTR_FIRST_BUS_DAY"])

    # spdf_all_systems = spdf_all_systems.loc[:,~spdf_all_systems.columns.duplicated()] -------- check at the end
    # spdf_all_systems = spdf_all_systems.filter(col("UTILIZATION_DAY_MEAN") != np.inf)


    # check spdf_all_systems dataframe, take only required columns
    cols_tbl =session.table('EDWSBX.INFORMATION_SCHEMA.columns').filter(f"TABLE_NAME = '{system_chunks_table.split('.')[-1]}' and TABLE_SCHEMA = 'TRAINING'").orderBy("ORDINAL_POSITION")

    col_list = []
    for row in cols_tbl.select("COLUMN_NAME").collect():
        col_list.append(row[0])


    # set(spdf_all_systems.columns)-set(col_list)


    # set(col_list)-set(spdf_all_systems.columns)
    col_list = set(col_list).intersection(set(spdf_all_systems.columns))
    len(col_list)


    spdf_all_systems = spdf_all_systems.select(col_list)


    # quick error handling for inf values in 'Percentage of OR Days in Qtr.' (setting to 0)
    spdf_all_systems = spdf_all_systems.replace({np.inf : 0}, subset=["PERCENTAGE_OR_DAYS_IN_QTR"])

    # print(set(spdf_all_systems['Qtr']))
    spdf_all_systems = spdf_all_systems.filter(col("QTR") >= current_qtr_offset_minus_1)



    #write system chunks to DB
    write_system_chunks(spdf_all_systems)


    # delete temporary tables
    for temp_table in temp_table_lst:
        del_temp_table = session.sql(f"DROP TABLE IF EXISTS EDWSBX.TRAINING.{temp_table} ").collect()



    # end_time = datetime.now()
    # # Total Time taken in the execution of script
    # # td = (end - start) .total_seconds() * 10**3
    # time_diff = (end_time - start_time)
    # print(time_diff)
    # 0:55:47.925125
    # 0:49:51.963339


    # log for day_durations
    day_durations_count = session.table(day_durations_table).filter(f" QTR>= {current_qtr_offset_minus_1} ").count()
    StgStatus = f"Inserted {day_durations_count} into {day_durations_table} for the quarters {current_qtr_offset_minus_1} to latest."
    query1 = "call EDWSBX.PROCESS.USP_LOGDETAILS({0}, {1}, '{2}', '{3}')".format(BatchID,ExeID,StgStatus,ProcName)
    session.sql(query1).collect()

    # log for procedure_data
    procedure_data_count = session.table(procedure_data_table).filter(f" CAL_YEAR_QTR>= {current_qtr_offset_minus_1} ").count()
    StgStatus = f"Inserted {procedure_data_count} into {procedure_data_table} for the quarters {current_qtr_offset_minus_1} to latest."
    query1 = "call EDWSBX.PROCESS.USP_LOGDETAILS({0}, {1}, '{2}', '{3}')".format(BatchID,ExeID,StgStatus,ProcName)
    session.sql(query1).collect()

    # log for system_chunks
    system_chunks_count = session.table(system_chunks_table).filter(f" QTR>= {current_qtr_offset_minus_1} ").count()
    StgStatus = f"Inserted {system_chunks_count} into {system_chunks_table} for the quarters {current_qtr_offset_minus_1} to latest."
    query1 = "call EDWSBX.PROCESS.USP_LOGDETAILS({0}, {1}, '{2}', '{3}')".format(BatchID,ExeID,StgStatus,ProcName)
    session.sql(query1).collect()

    #audit log for all three tables - processhistory
    TrgFinalCnt = day_durations_count + procedure_data_count + system_chunks_count
    StgStatus = f"""Inserted {day_durations_count} records into {day_durations_table.split(".")[-1]} | {procedure_data_count} records into {procedure_data_table.split(".")[-1]} | {system_chunks_count} records into {system_chunks_table.split(".")[-1]} """  
    
    query1 = "call EDWSBX.Process.USP_UPDATEAUDITDETAILS({0}, '{1}', {2}, {3}, {4}, {5}, {6}, '{7}')".format(BatchID,ProcName,StgCnt,InsertedCnt,UpdatedCnt,TrgIntialCnt,TrgFinalCnt,StgStatus) 
    session.sql(query1).collect()

    query1 = "call EDWSBX.Process.USP_UPDATEAUDITDETAILSONSUCCESS({0}, '{1}', {2}, {3}, '{4}')".format(BatchID,ProcName,UpdatedCnt,InsertedCnt,StgStatus)
    session.sql(query1).collect()

    query1 = "call EDWSBX.Process.USP_UPDATEAUDITDETAILSRUNTIMES({0}, '{1}')".format(BatchID,ProcName)
    session.sql(query1).collect()  

    return "SUCCESS"

def execute(session, BATCHID):
    ProcName = "TRAINING.USP_RUNNING_EFFICIENCY_UTILIZATION_METRICS_US_DATALOAD"
    BatchID = BATCHID
    res = ""
    try:
        res = load_data(session, BATCHID)
    except Exception as e:
        ErrorDESCRIPTION = str(e)
        ErrorPROC = ProcName
        ErrorLINE = sys.exc_info()[2].tb_lineno
        # Ensure proper formatting and escaping of error description
        ErrorDESCRIPTION = ErrorDESCRIPTION.replace("'", "''")  # Escape single quotes
        query = "call EDWSBX.Process.USP_UPDATEAUDITDETAILSONFAILURE({0}, '{1}', '{2}', {3}, '{4}')".format(BatchID, ProcName, ErrorDESCRIPTION, ErrorLINE, ErrorPROC)
        session.sql(query).collect()
        res = "FAILED: " + ErrorDESCRIPTION
    return res

$$;